package com.ruoyi.check.domain;

/**
 * 加密请求包装类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class EncryptedRequestWrapper {
    
    /**
     * 加密后的数据
     */
    private String encryptedData;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    public EncryptedRequestWrapper() {
    }
    
    public EncryptedRequestWrapper(String encryptedData, Long timestamp) {
        this.encryptedData = encryptedData;
        this.timestamp = timestamp;
    }
    
    public String getEncryptedData() {
        return encryptedData;
    }
    
    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    @Override
    public String toString() {
        return "EncryptedRequestWrapper{" +
                "encryptedData='" + encryptedData + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
} 