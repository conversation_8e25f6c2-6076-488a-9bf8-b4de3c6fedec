package com.ruoyi.check.service;

import com.ruoyi.check.domain.*;
import com.ruoyi.check.utils.VerifyResultHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

/**
 * 车辆验证服务
 */
@Service
public class VehicleVerifyService {
    
    private final Random random = new Random();
    
    /**
     * 行驶证批量验证
     */
    public VerifyResponseData verifyDrivingLicense(VehicleDrivingLicenseVerifyRequest request) {
        VerifyResponseData responseData = new VerifyResponseData();
        responseData.setVerifyType(request.getVerifyType());
        responseData.setTotal(request.getVehicles().size());
        
        List<VerifyResponseData.VerifyResult> results = new ArrayList<>();
        for (VehicleDrivingLicenseVerifyRequest.VehicleDrivingLicenseData data : request.getVehicles()) {
            VerifyResponseData.VerifyResult result = new VerifyResponseData.VerifyResult();
            result.setPlateNumber(data.getPlateNumber());
            result.setOwner(data.getOwner());
            
            // 模拟验证结果
            String[] verifyResults = {"一致", "不一致", "失效", "不存在"};
            String verifyResult = verifyResults[random.nextInt(verifyResults.length)];
            VerifyResultHelper.setResultCode(result, verifyResult);
            
            switch (verifyResult) {
                case "一致":
                    result.setMessage("验证成功");
                    result.setCorrectInfo(null);
                    break;
                case "不一致":
                    result.setMessage("车辆识别代码不匹配");
                    // 返回正确信息
                    HashMap<String, Object> correctInfo = new HashMap<>();
                    correctInfo.put("vehicleIdentificationCode", "LSGGG54X8DS111111");
                    correctInfo.put("vehicleType", "货车");
                    correctInfo.put("owner", data.getOwner());
                    result.setCorrectInfo(correctInfo);
                    break;
                case "失效":
                    result.setMessage("行驶证已失效");
                    result.setCorrectInfo(null);
                    break;
                case "不存在":
                    result.setMessage("行驶证不存在");
                    result.setCorrectInfo(null);
                    break;
            }
            
            results.add(result);
        }
        
        responseData.setResults(results);
        return responseData;
    }
    
    /**
     * 车辆道路运输证批量验证
     */
    public VerifyResponseData verifyRoadTransport(VehicleRoadTransportVerifyRequest request) {
        VerifyResponseData responseData = new VerifyResponseData();
        responseData.setVerifyType(request.getVerifyType());
        responseData.setTotal(request.getVehicles().size());
        
        List<VerifyResponseData.VerifyResult> results = new ArrayList<>();
        for (VehicleRoadTransportVerifyRequest.VehicleRoadTransportData data : request.getVehicles()) {
            VerifyResponseData.VerifyResult result = new VerifyResponseData.VerifyResult();
            result.setTransportLicenseNumber(data.getTransportLicenseNumber());
            result.setPlateNumber(data.getPlateNumber());
            
            // 模拟验证结果
            String[] verifyResults = {"一致", "不一致", "失效", "不存在"};
            String verifyResult = verifyResults[random.nextInt(verifyResults.length)];
            VerifyResultHelper.setResultCode(result, verifyResult);
            
            switch (verifyResult) {
                case "一致":
                    result.setMessage("验证成功");
                    result.setCorrectInfo(null);
                    break;
                case "不一致":
                    result.setMessage("业户名称不匹配");
                    // 返回正确信息
                    HashMap<String, Object> correctInfo = new HashMap<>();
                    correctInfo.put("plateNumber", data.getPlateNumber());
                    correctInfo.put("businessName", "正确的运输公司名称");
                    result.setCorrectInfo(correctInfo);
                    break;
                case "失效":
                    result.setMessage("道路运输证已失效");
                    result.setCorrectInfo(null);
                    break;
                case "不存在":
                    result.setMessage("道路运输证不存在");
                    result.setCorrectInfo(null);
                    break;
            }
            
            results.add(result);
        }
        
        responseData.setResults(results);
        return responseData;
    }
} 