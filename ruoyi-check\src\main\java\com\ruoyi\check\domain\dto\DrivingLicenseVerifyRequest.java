package com.ruoyi.check.domain.dto;

import java.util.List;

/**
 * 驾驶证验证请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class DrivingLicenseVerifyRequest {
    
    private String verifyType;  // 验证类型：authenticity（真实性）
    private List<LicenseData> licenses;
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<LicenseData> getLicenses() {
        return licenses;
    }
    
    public void setLicenses(List<LicenseData> licenses) {
        this.licenses = licenses;
    }
    
    public static class LicenseData {
        private String idCard;            // 身份证号
        private String name;              // 姓名
        private String licenseNumber;     // 驾驶证号
        private String licenseType;       // 驾驶证类型
        private String validStartDate;    // 有效期开始日期
        private String validEndDate;      // 有效期结束日期
        private String archiveNumber;     // 档案编号
        
        public LicenseData() {}
        
        public LicenseData(String idCard, String name, String licenseNumber, String licenseType, 
                          String validStartDate, String validEndDate, String archiveNumber) {
            this.idCard = idCard;
            this.name = name;
            this.licenseNumber = licenseNumber;
            this.licenseType = licenseType;
            this.validStartDate = validStartDate;
            this.validEndDate = validEndDate;
            this.archiveNumber = archiveNumber;
        }
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getLicenseNumber() {
            return licenseNumber;
        }
        
        public void setLicenseNumber(String licenseNumber) {
            this.licenseNumber = licenseNumber;
        }
        
        public String getLicenseType() {
            return licenseType;
        }
        
        public void setLicenseType(String licenseType) {
            this.licenseType = licenseType;
        }
        
        public String getValidStartDate() {
            return validStartDate;
        }
        
        public void setValidStartDate(String validStartDate) {
            this.validStartDate = validStartDate;
        }
        
        public String getValidEndDate() {
            return validEndDate;
        }
        
        public void setValidEndDate(String validEndDate) {
            this.validEndDate = validEndDate;
        }
        
        public String getArchiveNumber() {
            return archiveNumber;
        }
        
        public void setArchiveNumber(String archiveNumber) {
            this.archiveNumber = archiveNumber;
        }
    }
} 