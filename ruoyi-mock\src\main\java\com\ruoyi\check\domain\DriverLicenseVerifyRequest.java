package com.ruoyi.check.domain;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 司机驾驶证验证请求对象
 */
@ApiModel(value = "DriverLicenseVerifyRequest", description = "司机驾驶证验证请求")
public class DriverLicenseVerifyRequest {
    
    @ApiModelProperty(value = "验证类型：authenticity（真实性）| validity（有效性）", required = true, example = "authenticity")
    private String verifyType;  // 验证类型：authenticity（真实性）| validity（有效性）
    
    @ApiModelProperty(value = "驾驶证验证数据列表", required = true)
    private List<DriverLicenseData> licenses;
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<DriverLicenseData> getLicenses() {
        return licenses;
    }
    
    public void setLicenses(List<DriverLicenseData> licenses) {
        this.licenses = licenses;
    }
    
    @ApiModel(value = "DriverLicenseData", description = "司机驾驶证验证数据")
    public static class DriverLicenseData {
        @ApiModelProperty(value = "身份证号（密文）", required = true, example = "6105281122111138")
        private String idCard;          // 身份证号（密文）
        
        @ApiModelProperty(value = "姓名（明文）", required = true, example = "李四")
        private String name;            // 姓名（明文）
        
        @ApiModelProperty(value = "驾驶证号码", required = true, example = "610528199909191102")
        private String licenseNumber;   // 驾驶证号码
        
        @ApiModelProperty(value = "驾照类型", required = true, example = "B2")
        private String licenseType;     // 驾照类型
        
        @ApiModelProperty(value = "驾驶证档案编号", required = true, example = "610528001234")
        private String archiveNumber;   // 驾驶证档案编号
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getLicenseNumber() {
            return licenseNumber;
        }
        
        public void setLicenseNumber(String licenseNumber) {
            this.licenseNumber = licenseNumber;
        }
        
        public String getLicenseType() {
            return licenseType;
        }
        
        public void setLicenseType(String licenseType) {
            this.licenseType = licenseType;
        }
        
        public String getArchiveNumber() {
            return archiveNumber;
        }
        
        public void setArchiveNumber(String archiveNumber) {
            this.archiveNumber = archiveNumber;
        }
    }
} 