package com.ruoyi.check.service.impl;

import com.ruoyi.check.controller.VehicleTrackQueryRequest;
import com.ruoyi.check.service.IVehicleTrackService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 车辆轨迹查询服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class VehicleTrackServiceImpl implements IVehicleTrackService {
    
    private static final Logger logger = LoggerFactory.getLogger(VehicleTrackServiceImpl.class);
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 车辆轨迹查询接口URL
    @Value("${vehicle.track.history.url:http://localhost:8080/api/v1/huoda/vehicle/track/history}")
    private String vehicleTrackHistoryUrl;
    
    @Override
    public AjaxResult queryVehicleTrackHistory(String plateNumber, String loadTime, String arriveTime) {
        // 参数验证
        if (StringUtils.isEmpty(plateNumber)) {
            return AjaxResult.error("车牌号不能为空");
        }
        if (StringUtils.isEmpty(loadTime)) {
            return AjaxResult.error("装车时间不能为空");
        }
        if (StringUtils.isEmpty(arriveTime)) {
            return AjaxResult.error("运抵时间不能为空");
        }
        
        try {
            // 构建请求数据
            Map<String, Object> request = new HashMap<>();
            List<Map<String, Object>> vehicleList = new ArrayList<>();
            
            Map<String, Object> vehicleData = new HashMap<>();
            vehicleData.put("plateNumber", plateNumber);
            vehicleData.put("loadTime", loadTime);
            vehicleData.put("arriveTime", arriveTime);
            
            vehicleList.add(vehicleData);
            request.put("vehicles", vehicleList);
            
            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            // 调用Mock API
            ResponseEntity<AjaxResult> response = restTemplate.postForEntity(
                vehicleTrackHistoryUrl, entity, AjaxResult.class);
            
            if (response.getBody() != null) {
                logger.info("车辆轨迹查询成功，车牌号: {}", plateNumber);
                return response.getBody();
            } else {
                logger.error("车辆轨迹查询失败，响应为空，车牌号: {}", plateNumber);
                return AjaxResult.error("轨迹查询失败");
            }
            
        } catch (Exception e) {
            logger.error("调用车辆轨迹查询接口失败，车牌号: {}", plateNumber, e);
            return AjaxResult.error("轨迹查询失败: " + e.getMessage());
        }
    }
    
    @Override
    public AjaxResult queryVehicleTrackHistoryBatch(List<VehicleTrackQueryRequest.VehicleInfo> vehicles) {
        // 参数验证
        if (vehicles == null || vehicles.isEmpty()) {
            return AjaxResult.error("车辆信息列表不能为空");
        }
        
        try {
            // 构建请求数据
            Map<String, Object> request = new HashMap<>();
            List<Map<String, Object>> vehicleList = new ArrayList<>();
            
            for (VehicleTrackQueryRequest.VehicleInfo vehicle : vehicles) {
                // 验证单个车辆信息
                if (StringUtils.isEmpty(vehicle.getPlateNumber())) {
                    return AjaxResult.error("车牌号不能为空");
                }
                if (StringUtils.isEmpty(vehicle.getLoadTime())) {
                    return AjaxResult.error("装车时间不能为空");
                }
                if (StringUtils.isEmpty(vehicle.getArriveTime())) {
                    return AjaxResult.error("运抵时间不能为空");
                }
                
                Map<String, Object> vehicleData = new HashMap<>();
                vehicleData.put("plateNumber", vehicle.getPlateNumber());
                vehicleData.put("loadTime", vehicle.getLoadTime());
                vehicleData.put("arriveTime", vehicle.getArriveTime());
                
                vehicleList.add(vehicleData);
            }
            
            request.put("vehicles", vehicleList);
            
            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            // 调用Mock API
            ResponseEntity<AjaxResult> response = restTemplate.postForEntity(
                vehicleTrackHistoryUrl, entity, AjaxResult.class);
            
            if (response.getBody() != null) {
                logger.info("批量车辆轨迹查询成功，车辆数量: {}", vehicles.size());
                return response.getBody();
            } else {
                logger.error("批量车辆轨迹查询失败，响应为空，车辆数量: {}", vehicles.size());
                return AjaxResult.error("轨迹查询失败");
            }
            
        } catch (Exception e) {
            logger.error("调用批量车辆轨迹查询接口失败，车辆数量: {}", vehicles.size(), e);
            return AjaxResult.error("轨迹查询失败: " + e.getMessage());
        }
    }
} 