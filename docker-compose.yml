version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ruoyi-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: qlsz-check
      MYSQL_USER: ruoyi
      MYSQL_PASSWORD: ruoyi123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - ruoyi-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ruoyi-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - ruoyi-network

  # RuoYi应用
  ruoyi-app:
    build: .
    container_name: ruoyi-app
    restart: always
    ports:
      - "8973:8973"
    environment:
      # 数据库配置
      SPRING_DATASOURCE_URL: ************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 123456
      # Redis配置
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      # 应用配置
      SPRING_PROFILES_ACTIVE: prod
      SERVER_PORT: 8973
      # JVM配置
      JAVA_OPTS: -Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom
    volumes:
      - app_logs:/home/<USER>/logs
      - app_upload:/app/uploadPath
    depends_on:
      - mysql
      - redis
    networks:
      - ruoyi-network

volumes:
  mysql_data:
  redis_data:
  app_logs:
  app_upload:

networks:
  ruoyi-network:
    driver: bridge 