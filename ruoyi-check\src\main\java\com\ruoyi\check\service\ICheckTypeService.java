package com.ruoyi.check.service;

import java.util.List;
import com.ruoyi.check.domain.CheckType;

/**
 * 验证Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ICheckTypeService 
{
    /**
     * 查询验证
     * 
     * @param typeId 验证主键
     * @return 验证
     */
    public CheckType selectCheckTypeByTypeId(Long typeId);

    /**
     * 查询验证列表
     * 
     * @param checkType 验证
     * @return 验证集合
     */
    public List<CheckType> selectCheckTypeList(CheckType checkType);

    /**
     * 新增验证
     * 
     * @param checkType 验证
     * @return 结果
     */
    public int insertCheckType(CheckType checkType);

    /**
     * 修改验证
     * 
     * @param checkType 验证
     * @return 结果
     */
    public int updateCheckType(CheckType checkType);

    /**
     * 批量删除验证
     * 
     * @param typeIds 需要删除的验证主键集合
     * @return 结果
     */
    public int deleteCheckTypeByTypeIds(Long[] typeIds);

    /**
     * 删除验证信息
     * 
     * @param typeId 验证主键
     * @return 结果
     */
    public int deleteCheckTypeByTypeId(Long typeId);
}
