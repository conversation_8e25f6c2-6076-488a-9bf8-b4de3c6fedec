package com.ruoyi.check.domain;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 司机身份证验证请求对象
 */
@ApiModel(value = "DriverIdCardVerifyRequest", description = "司机身份证验证请求")
public class DriverIdCardVerifyRequest {
    
    @ApiModelProperty("验证类型：authenticity（真实性）| validity（有效性）")
    private String verifyType;  // 验证类型：authenticity（真实性）| validity（有效性）
    
    @ApiModelProperty("身份证验证数据列表")
    private List<DriverIdCardData> idCards;
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<DriverIdCardData> getIdCards() {
        return idCards;
    }
    
    public void setIdCards(List<DriverIdCardData> idCards) {
        this.idCards = idCards;
    }
    
    @ApiModel(value = "DriverIdCardData", description = "司机身份证验证数据")
    @JsonIgnoreProperties(ignoreUnknown = true)  // 忽略未知属性
    public static class DriverIdCardData {
        @ApiModelProperty("身份证号（密文）")
        private String idCard;   // 身份证号（密文）
        
        @ApiModelProperty("姓名（明文）")
        private String name;     // 姓名（明文）
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }
} 