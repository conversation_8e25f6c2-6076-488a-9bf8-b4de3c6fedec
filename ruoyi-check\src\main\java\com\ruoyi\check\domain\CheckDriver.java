package com.ruoyi.check.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 司机对象 check_driver
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class CheckDriver extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 司机主键 */
    private Long driverId;

    /** md_driver 主键ID */
    @Excel(name = "md_driver 主键ID")
    private Long mdDriverId;

    /** 承运商ID */
    @Excel(name = "承运商ID")
    private Long carrierId;

    /** 登录名：解决一个司机对应多个承运商 */
    @Excel(name = "登录名：解决一个司机对应多个承运商")
    private String loginName;

    /** 姓名 */
    @Excel(name = "姓名")
    private String NAME;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String TELEPHONE;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idNo;

    /** 身份证起始有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "身份证起始有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date idValidFrom;

    /** 身份证截至有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "身份证截至有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date idValidTo;

    /** 是否长期有效 */
    @Excel(name = "是否长期有效")
    private Integer idLongTerm;

    /** 驾照号 */
    @Excel(name = "驾照号")
    private String licenseNo;

    /** 准驾车型 */
    @Excel(name = "准驾车型")
    private String licenseClass;

    /** 驾照有效期（起始） */
    @Excel(name = "驾照有效期", readConverterExp = "起=始")
    private Date licenseValidFrom;

    /** 驾照有效期（结束） */
    @Excel(name = "驾照有效期", readConverterExp = "结=束")
    private Date licenseValidTo;

    /** 驾驶证是否长期有效 1-是 0-否 */
    @Excel(name = "驾驶证是否长期有效 1-是 0-否")
    private Integer licenseLongTerm;

    /** 从业资格证 */
    @Excel(name = "从业资格证")
    private String CERTIFICATE;

    /** 从业资格证发证机关 */
    @Excel(name = "从业资格证发证机关")
    private String certificateIssueOrg;

    /** 从业资格证有效期-开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "从业资格证有效期-开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date certificateFrom;

    /** 从业资格证有效期-结束 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "从业资格证有效期-结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date certificateTo;

    /** 修改人id */
    @Excel(name = "修改人id")
    private Long updateId;

    /** 驾驶证档案编号 */
    @Excel(name = "驾驶证档案编号")
    private String licenseFileNo;

    /** 验证创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "验证创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkCreateTime;

    /** 执行标识。0：未执行，1：已执行 */
    @Excel(name = "执行标识。0：未执行，1：已执行")
    private Long checkStatus;

    public void setDriverId(Long driverId) 
    {
        this.driverId = driverId;
    }

    public Long getDriverId() 
    {
        return driverId;
    }

    public void setMdDriverId(Long mdDriverId) 
    {
        this.mdDriverId = mdDriverId;
    }

    public Long getMdDriverId() 
    {
        return mdDriverId;
    }

    public void setCarrierId(Long carrierId) 
    {
        this.carrierId = carrierId;
    }

    public Long getCarrierId() 
    {
        return carrierId;
    }

    public void setLoginName(String loginName) 
    {
        this.loginName = loginName;
    }

    public String getLoginName() 
    {
        return loginName;
    }

    public void setNAME(String NAME) 
    {
        this.NAME = NAME;
    }

    public String getNAME() 
    {
        return NAME;
    }

    public void setTELEPHONE(String TELEPHONE) 
    {
        this.TELEPHONE = TELEPHONE;
    }

    public String getTELEPHONE() 
    {
        return TELEPHONE;
    }

    public void setIdNo(String idNo) 
    {
        this.idNo = idNo;
    }

    public String getIdNo() 
    {
        return idNo;
    }

    public void setIdValidFrom(Date idValidFrom) 
    {
        this.idValidFrom = idValidFrom;
    }

    public Date getIdValidFrom() 
    {
        return idValidFrom;
    }

    public void setIdValidTo(Date idValidTo) 
    {
        this.idValidTo = idValidTo;
    }

    public Date getIdValidTo() 
    {
        return idValidTo;
    }

    public void setIdLongTerm(Integer idLongTerm) 
    {
        this.idLongTerm = idLongTerm;
    }

    public Integer getIdLongTerm() 
    {
        return idLongTerm;
    }

    public void setLicenseNo(String licenseNo) 
    {
        this.licenseNo = licenseNo;
    }

    public String getLicenseNo() 
    {
        return licenseNo;
    }

    public void setLicenseClass(String licenseClass) 
    {
        this.licenseClass = licenseClass;
    }

    public String getLicenseClass() 
    {
        return licenseClass;
    }

    public void setLicenseValidFrom(Date licenseValidFrom) 
    {
        this.licenseValidFrom = licenseValidFrom;
    }

    public Date getLicenseValidFrom() 
    {
        return licenseValidFrom;
    }

    public void setLicenseValidTo(Date licenseValidTo) 
    {
        this.licenseValidTo = licenseValidTo;
    }

    public Date getLicenseValidTo() 
    {
        return licenseValidTo;
    }

    public void setLicenseLongTerm(Integer licenseLongTerm) 
    {
        this.licenseLongTerm = licenseLongTerm;
    }

    public Integer getLicenseLongTerm() 
    {
        return licenseLongTerm;
    }

    public void setCERTIFICATE(String CERTIFICATE) 
    {
        this.CERTIFICATE = CERTIFICATE;
    }

    public String getCERTIFICATE() 
    {
        return CERTIFICATE;
    }

    public void setCertificateIssueOrg(String certificateIssueOrg) 
    {
        this.certificateIssueOrg = certificateIssueOrg;
    }

    public String getCertificateIssueOrg() 
    {
        return certificateIssueOrg;
    }

    public void setCertificateFrom(Date certificateFrom) 
    {
        this.certificateFrom = certificateFrom;
    }

    public Date getCertificateFrom() 
    {
        return certificateFrom;
    }

    public void setCertificateTo(Date certificateTo) 
    {
        this.certificateTo = certificateTo;
    }

    public Date getCertificateTo() 
    {
        return certificateTo;
    }

    public void setUpdateId(Long updateId) 
    {
        this.updateId = updateId;
    }

    public Long getUpdateId() 
    {
        return updateId;
    }

    public void setLicenseFileNo(String licenseFileNo) 
    {
        this.licenseFileNo = licenseFileNo;
    }

    public String getLicenseFileNo() 
    {
        return licenseFileNo;
    }

    public void setCheckCreateTime(Date checkCreateTime) 
    {
        this.checkCreateTime = checkCreateTime;
    }

    public Date getCheckCreateTime() 
    {
        return checkCreateTime;
    }

    public void setCheckStatus(Long checkStatus) 
    {
        this.checkStatus = checkStatus;
    }

    public Long getCheckStatus() 
    {
        return checkStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("driverId", getDriverId())
            .append("mdDriverId", getMdDriverId())
            .append("carrierId", getCarrierId())
            .append("loginName", getLoginName())
            .append("NAME", getNAME())
            .append("TELEPHONE", getTELEPHONE())
            .append("idNo", getIdNo())
            .append("idValidFrom", getIdValidFrom())
            .append("idValidTo", getIdValidTo())
            .append("idLongTerm", getIdLongTerm())
            .append("licenseNo", getLicenseNo())
            .append("licenseClass", getLicenseClass())
            .append("licenseValidFrom", getLicenseValidFrom())
            .append("licenseValidTo", getLicenseValidTo())
            .append("licenseLongTerm", getLicenseLongTerm())
            .append("CERTIFICATE", getCERTIFICATE())
            .append("certificateIssueOrg", getCertificateIssueOrg())
            .append("certificateFrom", getCertificateFrom())
            .append("certificateTo", getCertificateTo())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("licenseFileNo", getLicenseFileNo())
            .append("checkCreateTime", getCheckCreateTime())
            .append("checkStatus", getCheckStatus())
            .toString();
    }
}
