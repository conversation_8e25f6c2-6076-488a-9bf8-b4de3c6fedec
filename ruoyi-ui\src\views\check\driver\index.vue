<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="md_driver 主键ID" prop="mdDriverId">
        <el-input
          v-model="queryParams.mdDriverId"
          placeholder="请输入md_driver 主键ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="承运商ID" prop="carrierId">
        <el-input
          v-model="queryParams.carrierId"
          placeholder="请输入承运商ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="登录名：解决一个司机对应多个承运商" prop="loginName">
        <el-input
          v-model="queryParams.loginName"
          placeholder="请输入登录名：解决一个司机对应多个承运商"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="NAME">
        <el-input
          v-model="queryParams.NAME"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="TELEPHONE">
        <el-input
          v-model="queryParams.TELEPHONE"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="idNo">
        <el-input
          v-model="queryParams.idNo"
          placeholder="请输入身份证号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份证起始有效期" prop="idValidFrom">
        <el-date-picker clearable
          v-model="queryParams.idValidFrom"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择身份证起始有效期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="身份证截至有效期" prop="idValidTo">
        <el-date-picker clearable
          v-model="queryParams.idValidTo"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择身份证截至有效期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否长期有效" prop="idLongTerm">
        <el-input
          v-model="queryParams.idLongTerm"
          placeholder="请输入是否长期有效"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="驾照号" prop="licenseNo">
        <el-input
          v-model="queryParams.licenseNo"
          placeholder="请输入驾照号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="准驾车型" prop="licenseClass">
        <el-input
          v-model="queryParams.licenseClass"
          placeholder="请输入准驾车型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="驾照有效期" prop="licenseValidFrom">
        <el-date-picker clearable
          v-model="queryParams.licenseValidFrom"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择驾照有效期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="驾照有效期" prop="licenseValidTo">
        <el-date-picker clearable
          v-model="queryParams.licenseValidTo"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择驾照有效期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="驾驶证是否长期有效 1-是 0-否" prop="licenseLongTerm">
        <el-input
          v-model="queryParams.licenseLongTerm"
          placeholder="请输入驾驶证是否长期有效 1-是 0-否"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="从业资格证" prop="CERTIFICATE">
        <el-input
          v-model="queryParams.CERTIFICATE"
          placeholder="请输入从业资格证"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="从业资格证发证机关" prop="certificateIssueOrg">
        <el-input
          v-model="queryParams.certificateIssueOrg"
          placeholder="请输入从业资格证发证机关"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="从业资格证有效期-开始" prop="certificateFrom">
        <el-date-picker clearable
          v-model="queryParams.certificateFrom"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择从业资格证有效期-开始">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="从业资格证有效期-结束" prop="certificateTo">
        <el-date-picker clearable
          v-model="queryParams.certificateTo"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择从业资格证有效期-结束">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="修改人id" prop="updateId">
        <el-input
          v-model="queryParams.updateId"
          placeholder="请输入修改人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改人" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入修改人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间" prop="updateTime">
        <el-date-picker clearable
          v-model="queryParams.updateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择修改时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="驾驶证档案编号" prop="licenseFileNo">
        <el-input
          v-model="queryParams.licenseFileNo"
          placeholder="请输入驾驶证档案编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="验证创建时间" prop="checkCreateTime">
        <el-date-picker clearable
          v-model="queryParams.checkCreateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择验证创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['check:driver:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['check:driver:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['check:driver:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['check:driver:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="driverList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="司机主键" align="center" prop="driverId" />
      <el-table-column label="md_driver 主键ID" align="center" prop="mdDriverId" />
      <el-table-column label="承运商ID" align="center" prop="carrierId" />
      <el-table-column label="登录名：解决一个司机对应多个承运商" align="center" prop="loginName" />
      <el-table-column label="姓名" align="center" prop="NAME" />
      <el-table-column label="联系电话" align="center" prop="TELEPHONE" />
      <el-table-column label="身份证号" align="center" prop="idNo" />
      <el-table-column label="身份证起始有效期" align="center" prop="idValidFrom" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.idValidFrom, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="身份证截至有效期" align="center" prop="idValidTo" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.idValidTo, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否长期有效" align="center" prop="idLongTerm" />
      <el-table-column label="驾照号" align="center" prop="licenseNo" />
      <el-table-column label="准驾车型" align="center" prop="licenseClass" />
      <el-table-column label="驾照有效期" align="center" prop="licenseValidFrom" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.licenseValidFrom, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="驾照有效期" align="center" prop="licenseValidTo" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.licenseValidTo, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="驾驶证是否长期有效 1-是 0-否" align="center" prop="licenseLongTerm" />
      <el-table-column label="从业资格证" align="center" prop="CERTIFICATE" />
      <el-table-column label="从业资格证发证机关" align="center" prop="certificateIssueOrg" />
      <el-table-column label="从业资格证有效期-开始" align="center" prop="certificateFrom" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.certificateFrom, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="从业资格证有效期-结束" align="center" prop="certificateTo" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.certificateTo, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改人id" align="center" prop="updateId" />
      <el-table-column label="修改人" align="center" prop="updateBy" />
      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="驾驶证档案编号" align="center" prop="licenseFileNo" />
      <el-table-column label="验证创建时间" align="center" prop="checkCreateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.checkCreateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="执行标识。0：未执行，1：已执行" align="center" prop="checkStatus" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['check:driver:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['check:driver:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改司机对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="md_driver 主键ID" prop="mdDriverId">
          <el-input v-model="form.mdDriverId" placeholder="请输入md_driver 主键ID" />
        </el-form-item>
        <el-form-item label="承运商ID" prop="carrierId">
          <el-input v-model="form.carrierId" placeholder="请输入承运商ID" />
        </el-form-item>
        <el-form-item label="登录名：解决一个司机对应多个承运商" prop="loginName">
          <el-input v-model="form.loginName" placeholder="请输入登录名：解决一个司机对应多个承运商" />
        </el-form-item>
        <el-form-item label="姓名" prop="NAME">
          <el-input v-model="form.NAME" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="TELEPHONE">
          <el-input v-model="form.TELEPHONE" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idNo">
          <el-input v-model="form.idNo" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="身份证起始有效期" prop="idValidFrom">
          <el-date-picker clearable
            v-model="form.idValidFrom"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择身份证起始有效期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="身份证截至有效期" prop="idValidTo">
          <el-date-picker clearable
            v-model="form.idValidTo"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择身份证截至有效期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否长期有效" prop="idLongTerm">
          <el-input v-model="form.idLongTerm" placeholder="请输入是否长期有效" />
        </el-form-item>
        <el-form-item label="驾照号" prop="licenseNo">
          <el-input v-model="form.licenseNo" placeholder="请输入驾照号" />
        </el-form-item>
        <el-form-item label="准驾车型" prop="licenseClass">
          <el-input v-model="form.licenseClass" placeholder="请输入准驾车型" />
        </el-form-item>
        <el-form-item label="驾照有效期" prop="licenseValidFrom">
          <el-date-picker clearable
            v-model="form.licenseValidFrom"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择驾照有效期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="驾照有效期" prop="licenseValidTo">
          <el-date-picker clearable
            v-model="form.licenseValidTo"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择驾照有效期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="驾驶证是否长期有效 1-是 0-否" prop="licenseLongTerm">
          <el-input v-model="form.licenseLongTerm" placeholder="请输入驾驶证是否长期有效 1-是 0-否" />
        </el-form-item>
        <el-form-item label="从业资格证" prop="CERTIFICATE">
          <el-input v-model="form.CERTIFICATE" placeholder="请输入从业资格证" />
        </el-form-item>
        <el-form-item label="从业资格证发证机关" prop="certificateIssueOrg">
          <el-input v-model="form.certificateIssueOrg" placeholder="请输入从业资格证发证机关" />
        </el-form-item>
        <el-form-item label="从业资格证有效期-开始" prop="certificateFrom">
          <el-date-picker clearable
            v-model="form.certificateFrom"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择从业资格证有效期-开始">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="从业资格证有效期-结束" prop="certificateTo">
          <el-date-picker clearable
            v-model="form.certificateTo"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择从业资格证有效期-结束">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="修改人id" prop="updateId">
          <el-input v-model="form.updateId" placeholder="请输入修改人id" />
        </el-form-item>
        <el-form-item label="驾驶证档案编号" prop="licenseFileNo">
          <el-input v-model="form.licenseFileNo" placeholder="请输入驾驶证档案编号" />
        </el-form-item>
        <el-form-item label="验证创建时间" prop="checkCreateTime">
          <el-date-picker clearable
            v-model="form.checkCreateTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择验证创建时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDriver, getDriver, delDriver, addDriver, updateDriver } from "@/api/check/driver"

export default {
  name: "Driver",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 司机表格数据
      driverList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mdDriverId: null,
        carrierId: null,
        loginName: null,
        NAME: null,
        TELEPHONE: null,
        idNo: null,
        idValidFrom: null,
        idValidTo: null,
        idLongTerm: null,
        licenseNo: null,
        licenseClass: null,
        licenseValidFrom: null,
        licenseValidTo: null,
        licenseLongTerm: null,
        CERTIFICATE: null,
        certificateIssueOrg: null,
        certificateFrom: null,
        certificateTo: null,
        createBy: null,
        createTime: null,
        updateId: null,
        updateBy: null,
        updateTime: null,
        licenseFileNo: null,
        checkCreateTime: null,
        checkStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        TELEPHONE: [
          { required: true, message: "联系电话不能为空", trigger: "blur" }
        ],
        createBy: [
          { required: true, message: "创建人不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        checkCreateTime: [
          { required: true, message: "验证创建时间不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询司机列表 */
    getList() {
      this.loading = true
      listDriver(this.queryParams).then(response => {
        this.driverList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        driverId: null,
        mdDriverId: null,
        carrierId: null,
        loginName: null,
        NAME: null,
        TELEPHONE: null,
        idNo: null,
        idValidFrom: null,
        idValidTo: null,
        idLongTerm: null,
        licenseNo: null,
        licenseClass: null,
        licenseValidFrom: null,
        licenseValidTo: null,
        licenseLongTerm: null,
        CERTIFICATE: null,
        certificateIssueOrg: null,
        certificateFrom: null,
        certificateTo: null,
        createBy: null,
        createTime: null,
        updateId: null,
        updateBy: null,
        updateTime: null,
        licenseFileNo: null,
        checkCreateTime: null,
        checkStatus: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.driverId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加司机"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const driverId = row.driverId || this.ids
      getDriver(driverId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改司机"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.driverId != null) {
            updateDriver(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addDriver(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const driverIds = row.driverId || this.ids
      this.$modal.confirm('是否确认删除司机编号为"' + driverIds + '"的数据项？').then(function() {
        return delDriver(driverIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('check/driver/export', {
        ...this.queryParams
      }, `driver_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
