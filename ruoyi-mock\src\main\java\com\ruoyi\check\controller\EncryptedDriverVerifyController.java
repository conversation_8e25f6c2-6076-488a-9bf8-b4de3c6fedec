package com.ruoyi.check.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.check.domain.EncryptedRequestWrapper;
import com.ruoyi.check.domain.*;
import com.ruoyi.check.service.DriverVerifyService;
import com.ruoyi.check.utils.AESUtils;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 加密司机验证控制器
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Api(tags = "加密司机验证接口")
@RestController
@RequestMapping("/api/v1/huoda/encrypted/driver/verify")
public class EncryptedDriverVerifyController {
    
    private static final Logger logger = LoggerFactory.getLogger(EncryptedDriverVerifyController.class);
    
    @Autowired
    private DriverVerifyService driverVerifyService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 加密手机号验证接口
     */
    @Anonymous
    @ApiOperation(value = "加密手机号验证", notes = "验证手机号与姓名的一致性（加密传输）")
    @PostMapping("/phone")
    public AjaxResult verifyPhoneEncrypted(@RequestBody EncryptedRequestWrapper encryptedRequest) {
        try {
            logger.info("接收到加密手机号验证请求");
            
            // 1. 解密请求数据
            String decryptedData = AESUtils.decrypt(encryptedRequest.getEncryptedData());
            logger.info("解密后的请求数据: {}", decryptedData);
            
            // 2. 反序列化为请求对象
            DriverPhoneVerifyRequest request = objectMapper.readValue(decryptedData, DriverPhoneVerifyRequest.class);
            
            // 3. 调用业务服务
            VerifyResponseData response = driverVerifyService.verifyPhone(request);
            
            // 4. 序列化响应数据
            String responseJson = objectMapper.writeValueAsString(response);
            
            // 5. 加密响应数据
            String encryptedResponse = AESUtils.encrypt(responseJson);
            
            // 6. 构建加密响应
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedResponse);
            result.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("请求处理成功", result);
            
        } catch (Exception e) {
            logger.error("加密手机号验证失败", e);
            return AjaxResult.error("加密手机号验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 加密身份证验证接口
     */
    @Anonymous
    @ApiOperation(value = "加密身份证验证", notes = "验证身份证号与姓名的一致性（加密传输）")
    @PostMapping("/idcard")
    public AjaxResult verifyIdCardEncrypted(@RequestBody EncryptedRequestWrapper encryptedRequest) {
        try {
            logger.info("接收到加密身份证验证请求");
            
            // 1. 解密请求数据
            String decryptedData = AESUtils.decrypt(encryptedRequest.getEncryptedData());
            logger.info("解密后的请求数据: {}", decryptedData);
            
            // 2. 反序列化为请求对象
            DriverIdCardVerifyRequest request = objectMapper.readValue(decryptedData, DriverIdCardVerifyRequest.class);
            
            // 3. 调用业务服务
            VerifyResponseData response = driverVerifyService.verifyIdCard(request);
            
            // 4. 序列化响应数据
            String responseJson = objectMapper.writeValueAsString(response);
            
            // 5. 加密响应数据
            String encryptedResponse = AESUtils.encrypt(responseJson);
            
            // 6. 构建加密响应
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedResponse);
            result.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("请求处理成功", result);
            
        } catch (Exception e) {
            logger.error("加密身份证验证失败", e);
            return AjaxResult.error("加密身份证验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 加密驾驶证验证接口
     */
    @Anonymous
    @ApiOperation(value = "加密驾驶证验证", notes = "验证驾驶证号与姓名的一致性（加密传输）")
    @PostMapping("/drivinglicense")
    public AjaxResult verifyDrivingLicenseEncrypted(@RequestBody EncryptedRequestWrapper encryptedRequest) {
        try {
            logger.info("接收到加密驾驶证验证请求");
            
            // 1. 解密请求数据
            String decryptedData = AESUtils.decrypt(encryptedRequest.getEncryptedData());
            logger.info("解密后的请求数据: {}", decryptedData);
            
            // 2. 反序列化为请求对象
            DriverLicenseVerifyRequest request = objectMapper.readValue(decryptedData, DriverLicenseVerifyRequest.class);
            
            // 3. 调用业务服务
            VerifyResponseData response = driverVerifyService.verifyDriverLicense(request);
            
            // 4. 序列化响应数据
            String responseJson = objectMapper.writeValueAsString(response);
            
            // 5. 加密响应数据
            String encryptedResponse = AESUtils.encrypt(responseJson);
            
            // 6. 构建加密响应
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedResponse);
            result.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("请求处理成功", result);
            
        } catch (Exception e) {
            logger.error("加密驾驶证验证失败", e);
            return AjaxResult.error("加密驾驶证验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 加密从业资格证验证接口
     */
    @Anonymous
    @ApiOperation(value = "加密从业资格证验证", notes = "验证从业资格证号与姓名的一致性（加密传输）")
    @PostMapping("/qualification")
    public AjaxResult verifyQualificationEncrypted(@RequestBody EncryptedRequestWrapper encryptedRequest) {
        try {
            logger.info("接收到加密从业资格证验证请求");
            
            // 1. 解密请求数据
            String decryptedData = AESUtils.decrypt(encryptedRequest.getEncryptedData());
            logger.info("解密后的请求数据: {}", decryptedData);
            
            // 2. 反序列化为请求对象
            DriverQualificationVerifyRequest request = objectMapper.readValue(decryptedData, DriverQualificationVerifyRequest.class);
            
            // 3. 调用业务服务
            VerifyResponseData response = driverVerifyService.verifyQualification(request);
            
            // 4. 序列化响应数据
            String responseJson = objectMapper.writeValueAsString(response);
            
            // 5. 加密响应数据
            String encryptedResponse = AESUtils.encrypt(responseJson);
            
            // 6. 构建加密响应
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedResponse);
            result.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("请求处理成功", result);
            
        } catch (Exception e) {
            logger.error("加密从业资格证验证失败", e);
            return AjaxResult.error("加密从业资格证验证失败: " + e.getMessage());
        }
    }
} 