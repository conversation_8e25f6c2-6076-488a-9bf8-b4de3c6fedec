# 服务端口配置
server:
  port: 8080
  servlet:
    context-path: /
    
# Spring配置
spring:
  application:
    name: ruoyi-mock
  profiles:
    active: dev

# AES加密配置（必须与ruoyi-check保持一致）
aes:
  encryption:
    # AES密钥（32字节，与ruoyi-check保持一致）
    key: MySecretKey12345MySecretKey12345
    # IV向量（16字节，与ruoyi-check保持一致）
    iv: 1234567890123456

# Swagger配置
swagger:
  enabled: true
  title: 货达Mock服务API
  description: 提供司机和车辆验证的模拟接口
  version: 1.0.0
  base-package: com.ruoyi.check.controller

# 日志配置
logging:
  level:
    com.ruoyi.check: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n' 