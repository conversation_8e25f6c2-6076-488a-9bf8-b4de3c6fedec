package com.ruoyi.check.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.check.utils.CryptoUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.SecretKey;
import java.security.PublicKey;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户端加密服务
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class ClientEncryptionService {
    
    private static final Logger logger = LoggerFactory.getLogger(ClientEncryptionService.class);
    
    @Autowired
    private RestTemplate restTemplate;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 服务端公钥获取URL
    @Value("${mock.server.public.key.url:http://localhost:8080/api/v1/huoda/secure/driver/verify/public-key}")
    private String publicKeyUrl;
    
    // 缓存的服务端公钥
    private PublicKey serverPublicKey;
    
    /**
     * 获取服务端公钥
     */
    private PublicKey getServerPublicKey() {
        if (serverPublicKey == null) {
            try {
                ResponseEntity<AjaxResult> response = restTemplate.getForEntity(publicKeyUrl, AjaxResult.class);
                if (response.getBody() != null && response.getBody().isSuccess()) {
                    String publicKeyStr = (String) response.getBody().get(AjaxResult.DATA_TAG);
                    serverPublicKey = CryptoUtils.stringToPublicKey(publicKeyStr);
                    logger.info("获取服务端公钥成功");
                } else {
                    throw new RuntimeException("获取服务端公钥失败");
                }
            } catch (Exception e) {
                logger.error("获取服务端公钥失败", e);
                throw new RuntimeException("获取服务端公钥失败", e);
            }
        }
        return serverPublicKey;
    }
    
    /**
     * 加密请求数据
     */
    public Map<String, Object> encryptRequest(Object requestData) {
        try {
            // 1. 获取服务端公钥
            PublicKey publicKey = getServerPublicKey();
            
            // 2. 生成AES密钥
            SecretKey aesKey = CryptoUtils.generateAESKey();
            
            // 3. 序列化请求数据
            String jsonData = objectMapper.writeValueAsString(requestData);
            
            // 4. 计算数据哈希值
            String dataHash = CryptoUtils.calculateSHA256(jsonData);
            
            // 5. 生成随机IV
            byte[] iv = CryptoUtils.generateIV();
            
            // 6. 使用AES密钥加密数据
            String encryptedData = CryptoUtils.aesEncrypt(jsonData, aesKey, iv);
            
            // 7. 使用服务端公钥加密AES密钥
            String encryptedAESKey = CryptoUtils.rsaEncrypt(
                CryptoUtils.aesKeyToString(aesKey), 
                publicKey
            );
            
            // 8. 构建加密请求
            Map<String, Object> encryptedRequest = new HashMap<>();
            encryptedRequest.put("encryptedAESKey", encryptedAESKey);
            encryptedRequest.put("iv", Base64.getEncoder().encodeToString(iv));
            encryptedRequest.put("encryptedData", encryptedData);
            encryptedRequest.put("dataHash", dataHash);
            encryptedRequest.put("timestamp", System.currentTimeMillis());
            
            logger.info("请求数据加密成功");
            return encryptedRequest;
            
        } catch (Exception e) {
            logger.error("加密请求数据失败", e);
            throw new RuntimeException("加密请求数据失败", e);
        }
    }
    
    /**
     * 解密响应数据
     */
    public <T> T decryptResponse(Map<String, Object> encryptedResponse, SecretKey aesKey, Class<T> targetClass) {
        try {
            // 1. 提取响应数据
            String iv = (String) encryptedResponse.get("iv");
            String encryptedData = (String) encryptedResponse.get("encryptedData");
            String dataHash = (String) encryptedResponse.get("dataHash");
            
            // 2. 解密IV
            byte[] ivBytes = Base64.getDecoder().decode(iv);
            
            // 3. 使用AES密钥解密数据
            String decryptedData = CryptoUtils.aesDecrypt(encryptedData, aesKey, ivBytes);
            
            // 4. 验证数据完整性
            if (!CryptoUtils.verifyIntegrity(decryptedData, dataHash)) {
                throw new SecurityException("响应数据完整性校验失败");
            }
            
            // 5. 反序列化为目标对象
            T result = objectMapper.readValue(decryptedData, targetClass);
            
            logger.info("响应数据解密成功");
            return result;
            
        } catch (Exception e) {
            logger.error("解密响应数据失败", e);
            throw new RuntimeException("解密响应数据失败", e);
        }
    }
    
    /**
     * 发送加密请求
     */
    public <T> T sendEncryptedRequest(String url, Object requestData, Class<T> responseClass) {
        try {
            // 1. 生成AES密钥（用于后续解密响应）
            SecretKey aesKey = CryptoUtils.generateAESKey();
            
            // 2. 加密请求数据
            Map<String, Object> encryptedRequest = encryptRequest(requestData);
            
            // 3. 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);
            
            // 4. 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getBody() != null) {
                // 5. 解密响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
                return decryptResponse(responseBody, aesKey, responseClass);
            } else {
                throw new RuntimeException("响应为空");
            }
            
        } catch (Exception e) {
            logger.error("发送加密请求失败: {}", url, e);
            throw new RuntimeException("发送加密请求失败", e);
        }
    }
    
    /**
     * 重置服务端公钥缓存
     */
    public void resetServerPublicKey() {
        serverPublicKey = null;
        logger.info("服务端公钥缓存已重置");
    }
} 