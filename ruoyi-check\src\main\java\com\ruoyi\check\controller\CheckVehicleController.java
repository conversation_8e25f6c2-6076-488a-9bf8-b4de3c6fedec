package com.ruoyi.check.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.check.domain.CheckVehicle;
import com.ruoyi.check.service.ICheckVehicleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 车辆信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/check/vehicle")
public class CheckVehicleController extends BaseController
{
    @Autowired
    private ICheckVehicleService checkVehicleService;

    /**
     * 查询车辆信息列表
     */
    @PreAuthorize("@ss.hasPermi('check:vehicle:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckVehicle checkVehicle)
    {
        startPage();
        List<CheckVehicle> list = checkVehicleService.selectCheckVehicleList(checkVehicle);
        return getDataTable(list);
    }

    /**
     * 导出车辆信息列表
     */
    @PreAuthorize("@ss.hasPermi('check:vehicle:export')")
    @Log(title = "车辆信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckVehicle checkVehicle)
    {
        List<CheckVehicle> list = checkVehicleService.selectCheckVehicleList(checkVehicle);
        ExcelUtil<CheckVehicle> util = new ExcelUtil<CheckVehicle>(CheckVehicle.class);
        util.exportExcel(response, list, "车辆信息数据");
    }

    /**
     * 获取车辆信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('check:vehicle:query')")
    @GetMapping(value = "/{vehicleId}")
    public AjaxResult getInfo(@PathVariable("vehicleId") Long vehicleId)
    {
        return success(checkVehicleService.selectCheckVehicleByVehicleId(vehicleId));
    }

    /**
     * 新增车辆信息
     */
    @PreAuthorize("@ss.hasPermi('check:vehicle:add')")
    @Log(title = "车辆信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckVehicle checkVehicle)
    {
        return toAjax(checkVehicleService.insertCheckVehicle(checkVehicle));
    }

    /**
     * 修改车辆信息
     */
    @PreAuthorize("@ss.hasPermi('check:vehicle:edit')")
    @Log(title = "车辆信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckVehicle checkVehicle)
    {
        return toAjax(checkVehicleService.updateCheckVehicle(checkVehicle));
    }

    /**
     * 删除车辆信息
     */
    @PreAuthorize("@ss.hasPermi('check:vehicle:remove')")
    @Log(title = "车辆信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{vehicleIds}")
    public AjaxResult remove(@PathVariable Long[] vehicleIds)
    {
        return toAjax(checkVehicleService.deleteCheckVehicleByVehicleIds(vehicleIds));
    }
}
