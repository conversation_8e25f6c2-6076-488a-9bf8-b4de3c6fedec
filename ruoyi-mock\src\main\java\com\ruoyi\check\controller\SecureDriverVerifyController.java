//package com.ruoyi.check.controller;
//
//import com.ruoyi.check.domain.*;
//import com.ruoyi.check.service.DriverVerifyService;
//import com.ruoyi.check.service.EncryptionService;
//import com.ruoyi.check.service.KeyManagementService;
//import com.ruoyi.common.annotation.Anonymous;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiImplicitParam;
//
//import javax.crypto.SecretKey;
//
///**
// * 安全司机验证控制器（加密传输）
// */
//@Api(tags = "货达Mock服务-安全司机验证接口", description = "提供司机相关证件的加密批量验证服务")
//@RestController
//@RequestMapping("/api/v1/huoda/secure/driver/verify")
//public class SecureDriverVerifyController extends BaseController {
//
//    @Autowired
//    private DriverVerifyService driverVerifyService;
//
//    @Autowired
//    private EncryptionService encryptionService;
//
//    @Autowired
//    private KeyManagementService keyManagementService;
//
//    /**
//     * 获取服务端公钥
//     */
//    @ApiOperation(value = "获取服务端RSA公钥",
//                  notes = "客户端需要先获取服务端公钥，用于加密AES密钥")
//    @Anonymous
//    @GetMapping("/public-key")
//    public AjaxResult getPublicKey() {
//        String publicKey = keyManagementService.getServerPublicKeyString();
//        return AjaxResult.success("获取公钥成功", publicKey);
//    }
//
//    /**
//     * 手机号码批量验证接口（加密）
//     */
//    @ApiOperation(value = "手机号码批量验证（加密传输）",
//                  notes = "验证手机号码与身份证、姓名的一致性。使用AES+RSA混合加密确保传输安全。\n\n" +
//                         "加密流程：\n" +
//                         "1. 客户端生成AES密钥\n" +
//                         "2. 使用AES密钥加密请求数据\n" +
//                         "3. 使用服务端RSA公钥加密AES密钥\n" +
//                         "4. 发送加密请求\n" +
//                         "5. 服务端解密并处理\n" +
//                         "6. 使用相同AES密钥加密响应数据")
//    @ApiImplicitParam(name = "encryptedRequest", value = "加密的请求数据", required = true, dataTypeClass = EncryptedRequest.class)
//    @Anonymous
//    @PostMapping("/phone")
//    public EncryptedResponse verifyPhoneSecure(@RequestBody EncryptedRequest encryptedRequest) {
//        // 1. 解密请求数据
//        DriverPhoneVerifyRequest request = encryptionService.decryptRequest(
//            encryptedRequest, DriverPhoneVerifyRequest.class);
//
//        // 2. 处理业务逻辑
//        VerifyResponseData responseData = driverVerifyService.verifyPhone(request);
//
//        // 3. 提取AES密钥用于响应加密
//        SecretKey aesKey = encryptionService.extractAESKey(encryptedRequest);
//
//        // 4. 加密响应数据
//        return encryptionService.encryptResponse(responseData, aesKey);
//    }
//
//    /**
//     * 身份证批量验证接口（加密）
//     */
//    @ApiOperation(value = "身份证批量验证（加密传输）",
//                  notes = "验证身份证号码与姓名的一致性，支持真实性和有效性验证。使用AES+RSA混合加密确保传输安全。")
//    @ApiImplicitParam(name = "encryptedRequest", value = "加密的请求数据", required = true, dataTypeClass = EncryptedRequest.class)
//    @Anonymous
//    @PostMapping("/idcard")
//    public EncryptedResponse verifyIdCardSecure(@RequestBody EncryptedRequest encryptedRequest) {
//        // 1. 解密请求数据
//        DriverIdCardVerifyRequest request = encryptionService.decryptRequest(
//            encryptedRequest, DriverIdCardVerifyRequest.class);
//
//        // 2. 处理业务逻辑
//        VerifyResponseData responseData = driverVerifyService.verifyIdCard(request);
//
//        // 3. 提取AES密钥用于响应加密
//        SecretKey aesKey = encryptionService.extractAESKey(encryptedRequest);
//
//        // 4. 加密响应数据
//        return encryptionService.encryptResponse(responseData, aesKey);
//    }
//
//    /**
//     * 驾驶证批量验证接口（加密）
//     */
//    @ApiOperation(value = "驾驶证批量验证（加密传输）",
//                  notes = "验证驾驶证信息的真实性和有效性。支持验证驾驶证号码、身份证号、姓名、驾照类型、有效期等信息的一致性。使用AES+RSA混合加密确保传输安全。")
//    @ApiImplicitParam(name = "encryptedRequest", value = "加密的请求数据", required = true, dataTypeClass = EncryptedRequest.class)
//    @Anonymous
//    @PostMapping("/drivinglicense")
//    public EncryptedResponse verifyDriverLicenseSecure(@RequestBody EncryptedRequest encryptedRequest) {
//        // 1. 解密请求数据
//        DriverLicenseVerifyRequest request = encryptionService.decryptRequest(
//            encryptedRequest, DriverLicenseVerifyRequest.class);
//
//        // 2. 处理业务逻辑
//        VerifyResponseData responseData = driverVerifyService.verifyDriverLicense(request);
//
//        // 3. 提取AES密钥用于响应加密
//        SecretKey aesKey = encryptionService.extractAESKey(encryptedRequest);
//
//        // 4. 加密响应数据
//        return encryptionService.encryptResponse(responseData, aesKey);
//    }
//
//    /**
//     * 从业资格证批量验证接口（加密）
//     */
//    @ApiOperation(value = "从业资格证批量验证（加密传输）",
//                  notes = "验证从业资格证信息的真实性和有效性，包括证件编号、姓名、有效期等信息。使用AES+RSA混合加密确保传输安全。")
//    @ApiImplicitParam(name = "encryptedRequest", value = "加密的请求数据", required = true, dataTypeClass = EncryptedRequest.class)
//    @Anonymous
//    @PostMapping("/qualification")
//    public EncryptedResponse verifyQualificationSecure(@RequestBody EncryptedRequest encryptedRequest) {
//        // 1. 解密请求数据
//        DriverQualificationVerifyRequest request = encryptionService.decryptRequest(
//            encryptedRequest, DriverQualificationVerifyRequest.class);
//
//        // 2. 处理业务逻辑
//        VerifyResponseData responseData = driverVerifyService.verifyQualification(request);
//
//        // 3. 提取AES密钥用于响应加密
//        SecretKey aesKey = encryptionService.extractAESKey(encryptedRequest);
//
//        // 4. 加密响应数据
//        return encryptionService.encryptResponse(responseData, aesKey);
//    }
//}