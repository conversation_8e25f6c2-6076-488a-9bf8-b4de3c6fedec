package com.ruoyi.check.domain.dto;

import java.util.List;

/**
 * 驾驶证验证响应DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class DrivingLicenseVerifyResponse {
    
    private String msg;
    private Integer code;
    private LicenseData data;
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public LicenseData getData() {
        return data;
    }
    
    public void setData(LicenseData data) {
        this.data = data;
    }
    
    public static class LicenseData {
        private String verifyType;
        private Integer total;
        private List<LicenseResult> results;
        
        public String getVerifyType() {
            return verifyType;
        }
        
        public void setVerifyType(String verifyType) {
            this.verifyType = verifyType;
        }
        
        public Integer getTotal() {
            return total;
        }
        
        public void setTotal(Integer total) {
            this.total = total;
        }
        
        public List<LicenseResult> getResults() {
            return results;
        }
        
        public void setResults(List<LicenseResult> results) {
            this.results = results;
        }
    }
    
    public static class LicenseResult {
        private String phone;
        private String idCard;
        private String name;
        private String licenseNumber;
        private String qualificationNumber;
        private String plateNumber;
        private String transportLicenseNumber;
        private String owner;
        private Boolean formatValid;
        private String verifyResult;
        private String message;
        private String correctInfo;
        
        public String getPhone() {
            return phone;
        }
        
        public void setPhone(String phone) {
            this.phone = phone;
        }
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getLicenseNumber() {
            return licenseNumber;
        }
        
        public void setLicenseNumber(String licenseNumber) {
            this.licenseNumber = licenseNumber;
        }
        
        public String getQualificationNumber() {
            return qualificationNumber;
        }
        
        public void setQualificationNumber(String qualificationNumber) {
            this.qualificationNumber = qualificationNumber;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getTransportLicenseNumber() {
            return transportLicenseNumber;
        }
        
        public void setTransportLicenseNumber(String transportLicenseNumber) {
            this.transportLicenseNumber = transportLicenseNumber;
        }
        
        public String getOwner() {
            return owner;
        }
        
        public void setOwner(String owner) {
            this.owner = owner;
        }
        
        public Boolean getFormatValid() {
            return formatValid;
        }
        
        public void setFormatValid(Boolean formatValid) {
            this.formatValid = formatValid;
        }
        
        public String getVerifyResult() {
            return verifyResult;
        }
        
        public void setVerifyResult(String verifyResult) {
            this.verifyResult = verifyResult;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public String getCorrectInfo() {
            return correctInfo;
        }
        
        public void setCorrectInfo(String correctInfo) {
            this.correctInfo = correctInfo;
        }
    }
} 