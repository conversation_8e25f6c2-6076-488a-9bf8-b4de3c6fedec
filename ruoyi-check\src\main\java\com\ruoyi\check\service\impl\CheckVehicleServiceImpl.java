package com.ruoyi.check.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.check.mapper.CheckVehicleMapper;
import com.ruoyi.check.domain.CheckVehicle;
import com.ruoyi.check.service.ICheckVehicleService;

/**
 * 车辆信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class CheckVehicleServiceImpl implements ICheckVehicleService 
{
    @Autowired
    private CheckVehicleMapper checkVehicleMapper;

    /**
     * 查询车辆信息
     * 
     * @param vehicleId 车辆信息主键
     * @return 车辆信息
     */
    @Override
    public CheckVehicle selectCheckVehicleByVehicleId(Long vehicleId)
    {
        return checkVehicleMapper.selectCheckVehicleByVehicleId(vehicleId);
    }

    /**
     * 查询车辆信息列表
     * 
     * @param checkVehicle 车辆信息
     * @return 车辆信息
     */
    @Override
    public List<CheckVehicle> selectCheckVehicleList(CheckVehicle checkVehicle)
    {
        return checkVehicleMapper.selectCheckVehicleList(checkVehicle);
    }

    /**
     * 新增车辆信息
     * 
     * @param checkVehicle 车辆信息
     * @return 结果
     */
    @Override
    public int insertCheckVehicle(CheckVehicle checkVehicle)
    {
        checkVehicle.setCreateTime(DateUtils.getNowDate());
        return checkVehicleMapper.insertCheckVehicle(checkVehicle);
    }

    /**
     * 修改车辆信息
     * 
     * @param checkVehicle 车辆信息
     * @return 结果
     */
    @Override
    public int updateCheckVehicle(CheckVehicle checkVehicle)
    {
        checkVehicle.setUpdateTime(DateUtils.getNowDate());
        return checkVehicleMapper.updateCheckVehicle(checkVehicle);
    }

    /**
     * 批量删除车辆信息
     * 
     * @param vehicleIds 需要删除的车辆信息主键集合
     * @return 结果
     */
    @Override
    public int deleteCheckVehicleByVehicleIds(Long[] vehicleIds)
    {
        return checkVehicleMapper.deleteCheckVehicleByVehicleIds(vehicleIds);
    }

    /**
     * 删除车辆信息信息
     * 
     * @param vehicleId 车辆信息主键
     * @return 结果
     */
    @Override
    public int deleteCheckVehicleByVehicleId(Long vehicleId)
    {
        return checkVehicleMapper.deleteCheckVehicleByVehicleId(vehicleId);
    }
    
    /**
     * 分页查询车辆列表（用于批量处理）
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 车辆集合
     */
    @Override
    public List<CheckVehicle> selectCheckVehicleListByPage(Long offset, Integer limit) {
        return checkVehicleMapper.selectCheckVehicleListByPage(offset, limit);
    }
    
    /**
     * 统计车辆总数
     * 
     * @return 总数
     */
    @Override
    public long countCheckVehicles() {
        return checkVehicleMapper.countCheckVehicles();
    }
}
