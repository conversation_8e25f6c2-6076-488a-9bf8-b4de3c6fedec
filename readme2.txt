介绍：
    技术介绍：这是一个ruoyi-vue的项目，框架是springboot和vue
    背景介绍：要做的是一个司机和车辆验证的需求。我们是甲方，我们有一些
    信息想调用乙方的接口去验证。验真的信息如下：
    1．司机基础数据验证
       (1)手机号码验证：验证手机号及手机号开卡人一致性。
       (2)身份证验证：验证身份证号、姓名的一致性正确性。
       (3)驾驶证验证：验证驾驶证的真实性，校验项包括姓名、身份证号/驾驶证号码、驾照类型（如B2）、驾驶证有效性期、驾驶证档案编号。
       (4)从业资格证：验证从业资格证的真实性，验证的数据项包括姓名、从业资格证编号、从业资格证有效性。
    2. 车辆基础数据验证
       (1)行驶证验证：验证行驶证的真实性及有效性。验证的信息包括车牌号、车辆识别代码、车辆类型、所有人及证件有效性。
       (2)车辆道路运输证：验证车辆道路运输证的真实性及有效性。通过车辆道路运输证编号、车牌号、业户名称，验证车辆道路运输证的真实性及有效性。

    这两个需求设计到的模块是
    ruoyi-check: 这个模块是我们写的，我们在这个模块中要进行“司机基础数据验证”和“车辆基础数据验证”
    我们在这个模块中会进行远程调用，调用乙方写的代码。这里的乙方还没有实现，我专门模拟了一个ruoyi-mock
    这个模块，这个模块是用来模拟乙方的，等乙方实现完后，调用的url会变成真实的乙方的url。
    调用的思路是我们从数据库中查询出“车辆表”和“司机表”，然后把这些数据批量调用ruoyi-mock的验证方法。
    CheckDriverController和CheckVehicleController 分别对应“司机表”和“车辆表”的增删改查
    DriverVerifyBatchController和VehicleVerifyBatchController 分别对应“司机基础数据验证”和“车辆基础数据验证”

    ruoyi-mock：这个模块是我们写的模拟乙方的代码，以后乙方写完这个模块就不用了。


ruoyi-admin是一个聚合项目，他聚合了ruoyi-check和ruoyi-mock。现在的框架架构我觉得可以的



1 非mock司机身份证真实性验证 完成
2 非mock司机身份证有效性验证
3 非mock司机手机号验证
4 非mock驾驶证真实性验证
5 非mock驾驶证有效性验证
6 非mock从业资格证真实性验证