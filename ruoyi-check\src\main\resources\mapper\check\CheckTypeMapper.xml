<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.check.mapper.CheckTypeMapper">
    
    <resultMap type="CheckType" id="CheckTypeResult">
        <result property="typeId"    column="type_id"    />
        <result property="checkData"    column="check_data"    />
        <result property="checkName"    column="check_name"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="dataStatus"    column="data_status"    />
        <result property="hdResult"    column="hd_result"    />
        <result property="hsResult"    column="hs_result"    />
        <result property="wbResult"    column="wb_result"    />
        <result property="kycResult"    column="kyc_result"    />
        <result property="ddcResult"    column="ddc_result"    />
        <result property="detail"    column="detail"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCheckTypeVo">
        select type_id, check_data, check_name, check_status, data_status, hd_result, hs_result, wb_result, kyc_result, ddc_result, detail, del_flag, create_by, create_time, update_by, update_time, remark from check_type
    </sql>

    <select id="selectCheckTypeList" parameterType="CheckType" resultMap="CheckTypeResult">
        <include refid="selectCheckTypeVo"/>
        <where>  
            <if test="checkData != null  and checkData != ''"> and check_data = #{checkData}</if>
            <if test="checkName != null  and checkName != ''"> and check_name like concat('%', #{checkName}, '%')</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
            <if test="dataStatus != null  and dataStatus != ''"> and data_status = #{dataStatus}</if>
            <if test="hdResult != null  and hdResult != ''"> and hd_result = #{hdResult}</if>
            <if test="hsResult != null  and hsResult != ''"> and hs_result = #{hsResult}</if>
            <if test="wbResult != null  and wbResult != ''"> and wb_result = #{wbResult}</if>
            <if test="kycResult != null  and kycResult != ''"> and kyc_result = #{kycResult}</if>
            <if test="ddcResult != null  and ddcResult != ''"> and ddc_result = #{ddcResult}</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
        </where>
    </select>
    
    <select id="selectCheckTypeByTypeId" parameterType="Long" resultMap="CheckTypeResult">
        <include refid="selectCheckTypeVo"/>
        where type_id = #{typeId}
    </select>

    <insert id="insertCheckType" parameterType="CheckType" useGeneratedKeys="true" keyProperty="typeId">
        insert into check_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checkData != null">check_data,</if>
            <if test="checkName != null">check_name,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="dataStatus != null">data_status,</if>
            <if test="hdResult != null and hdResult != ''">hd_result,</if>
            <if test="hsResult != null and hsResult != ''">hs_result,</if>
            <if test="wbResult != null and wbResult != ''">wb_result,</if>
            <if test="kycResult != null and kycResult != ''">kyc_result,</if>
            <if test="ddcResult != null and ddcResult != ''">ddc_result,</if>
            <if test="detail != null">detail,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checkData != null">#{checkData},</if>
            <if test="checkName != null">#{checkName},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="dataStatus != null">#{dataStatus},</if>
            <if test="hdResult != null and hdResult != ''">#{hdResult},</if>
            <if test="hsResult != null and hsResult != ''">#{hsResult},</if>
            <if test="wbResult != null and wbResult != ''">#{wbResult},</if>
            <if test="kycResult != null and kycResult != ''">#{kycResult},</if>
            <if test="ddcResult != null and ddcResult != ''">#{ddcResult},</if>
            <if test="detail != null">#{detail},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCheckType" parameterType="CheckType">
        update check_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkData != null">check_data = #{checkData},</if>
            <if test="checkName != null">check_name = #{checkName},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="dataStatus != null">data_status = #{dataStatus},</if>
            <if test="hdResult != null and hdResult != ''">hd_result = #{hdResult},</if>
            <if test="hsResult != null and hsResult != ''">hs_result = #{hsResult},</if>
            <if test="wbResult != null and wbResult != ''">wb_result = #{wbResult},</if>
            <if test="kycResult != null and kycResult != ''">kyc_result = #{kycResult},</if>
            <if test="ddcResult != null and ddcResult != ''">ddc_result = #{ddcResult},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where type_id = #{typeId}
    </update>

    <delete id="deleteCheckTypeByTypeId" parameterType="Long">
        delete from check_type where type_id = #{typeId}
    </delete>

    <delete id="deleteCheckTypeByTypeIds" parameterType="String">
        delete from check_type where type_id in 
        <foreach item="typeId" collection="array" open="(" separator="," close=")">
            #{typeId}
        </foreach>
    </delete>
</mapper>