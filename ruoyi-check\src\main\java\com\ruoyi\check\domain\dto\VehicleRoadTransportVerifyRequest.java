package com.ruoyi.check.domain.dto;

import java.util.List;

/**
 * 车辆道路运输证验证请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class VehicleRoadTransportVerifyRequest {
    
    private String verifyType;  // 验证类型：authenticity（真实性）| validity（有效性）
    private List<VehicleData> vehicles;
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<VehicleData> getVehicles() {
        return vehicles;
    }
    
    public void setVehicles(List<VehicleData> vehicles) {
        this.vehicles = vehicles;
    }
    
    public static class VehicleData {
        private String transportLicenseNumber;  // 车辆道路运输证编号
        private String plateNumber;             // 车牌号
        private String businessName;            // 业户名称
        private String validEndDate;            // 道路运输证到期日
        
        public VehicleData() {}
        
        public VehicleData(String transportLicenseNumber, String plateNumber, String businessName, String validEndDate) {
            this.transportLicenseNumber = transportLicenseNumber;
            this.plateNumber = plateNumber;
            this.businessName = businessName;
            this.validEndDate = validEndDate;
        }
        
        public String getTransportLicenseNumber() {
            return transportLicenseNumber;
        }
        
        public void setTransportLicenseNumber(String transportLicenseNumber) {
            this.transportLicenseNumber = transportLicenseNumber;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getBusinessName() {
            return businessName;
        }
        
        public void setBusinessName(String businessName) {
            this.businessName = businessName;
        }
        
        public String getValidEndDate() {
            return validEndDate;
        }
        
        public void setValidEndDate(String validEndDate) {
            this.validEndDate = validEndDate;
        }
    }
}
