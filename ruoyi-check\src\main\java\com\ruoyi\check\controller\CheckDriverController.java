package com.ruoyi.check.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.check.domain.CheckDriver;
import com.ruoyi.check.service.ICheckDriverService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 司机Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/check/driver")
public class CheckDriverController extends BaseController
{
    @Autowired
    private ICheckDriverService checkDriverService;

    /**
     * 查询司机列表
     */
    @PreAuthorize("@ss.hasPermi('check:driver:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckDriver checkDriver)
    {
        startPage();
        List<CheckDriver> list = checkDriverService.selectCheckDriverList(checkDriver);
        return getDataTable(list);
    }

    /**
     * 导出司机列表
     */
    @PreAuthorize("@ss.hasPermi('check:driver:export')")
    @Log(title = "司机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckDriver checkDriver)
    {
        List<CheckDriver> list = checkDriverService.selectCheckDriverList(checkDriver);
        ExcelUtil<CheckDriver> util = new ExcelUtil<CheckDriver>(CheckDriver.class);
        util.exportExcel(response, list, "司机数据");
    }

    /**
     * 获取司机详细信息
     */
    @PreAuthorize("@ss.hasPermi('check:driver:query')")
    @GetMapping(value = "/{driverId}")
    public AjaxResult getInfo(@PathVariable("driverId") Long driverId)
    {
        return success(checkDriverService.selectCheckDriverByDriverId(driverId));
    }

    /**
     * 新增司机
     */
    @PreAuthorize("@ss.hasPermi('check:driver:add')")
    @Log(title = "司机", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckDriver checkDriver)
    {
        return toAjax(checkDriverService.insertCheckDriver(checkDriver));
    }

    /**
     * 修改司机
     */
    @PreAuthorize("@ss.hasPermi('check:driver:edit')")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckDriver checkDriver)
    {
        return toAjax(checkDriverService.updateCheckDriver(checkDriver));
    }

    /**
     * 删除司机
     */
    @PreAuthorize("@ss.hasPermi('check:driver:remove')")
    @Log(title = "司机", businessType = BusinessType.DELETE)
	@DeleteMapping("/{driverIds}")
    public AjaxResult remove(@PathVariable Long[] driverIds)
    {
        return toAjax(checkDriverService.deleteCheckDriverByDriverIds(driverIds));
    }
}
