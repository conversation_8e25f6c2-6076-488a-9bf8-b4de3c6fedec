package com.ruoyi.check.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
//@Api("货达Mock服务-测试接口")
@RestController
@RequestMapping("/api/v1/huoda/test")
public class TestCheckController extends BaseController {
    
    /**
     * 健康检查接口
     */
//    @ApiOperation("健康检查")
    @GetMapping("/health")
    public AjaxResult health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("carrier", "huoda");
        data.put("module", "check-mock");
        data.put("timestamp", System.currentTimeMillis());
        
        return AjaxResult.success("服务正常", data);
    }
    
    /**
     * 接口列表
     */
//    @ApiOperation("获取接口列表")
    @GetMapping("/endpoints")
    public AjaxResult endpoints() {
        Map<String, Object> data = new HashMap<>();
        
        Map<String, String> driverEndpoints = new HashMap<>();
        driverEndpoints.put("手机号验证", "POST /api/v1/huoda/driver/verify/phone");
        driverEndpoints.put("身份证验证", "POST /api/v1/huoda/driver/verify/idcard");
        driverEndpoints.put("驾驶证验证", "POST /api/v1/huoda/driver/verify/drivinglicense");
        driverEndpoints.put("从业资格证验证", "POST /api/v1/huoda/driver/verify/qualification");
        
        Map<String, String> vehicleEndpoints = new HashMap<>();
        vehicleEndpoints.put("行驶证验证", "POST /api/v1/huoda/vehicle/verify/drivinglicense");
        vehicleEndpoints.put("道路运输证验证", "POST /api/v1/huoda/vehicle/verify/roadtransport");
        vehicleEndpoints.put("车辆轨迹查询", "POST /api/v1/huoda/vehicle/track/history");
        
        data.put("司机验证接口", driverEndpoints);
        data.put("车辆验证接口", vehicleEndpoints);
        
        return AjaxResult.success("接口列表获取成功", data);
    }
} 