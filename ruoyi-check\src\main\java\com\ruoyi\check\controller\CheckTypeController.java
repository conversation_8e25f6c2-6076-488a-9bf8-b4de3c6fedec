package com.ruoyi.check.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.check.domain.CheckType;
import com.ruoyi.check.service.ICheckTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 验证Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/check/type")
public class CheckTypeController extends BaseController
{
    @Autowired
    private ICheckTypeService checkTypeService;

    /**
     * 查询验证列表
     */
    @PreAuthorize("@ss.hasPermi('check:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckType checkType)
    {
        startPage();
        List<CheckType> list = checkTypeService.selectCheckTypeList(checkType);
        return getDataTable(list);
    }

    /**
     * 导出验证列表
     */
    @PreAuthorize("@ss.hasPermi('check:type:export')")
    @Log(title = "验证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckType checkType)
    {
        List<CheckType> list = checkTypeService.selectCheckTypeList(checkType);
        ExcelUtil<CheckType> util = new ExcelUtil<CheckType>(CheckType.class);
        util.exportExcel(response, list, "验证数据");
    }

    /**
     * 获取验证详细信息
     */
    @PreAuthorize("@ss.hasPermi('check:type:query')")
    @GetMapping(value = "/{typeId}")
    public AjaxResult getInfo(@PathVariable("typeId") Long typeId)
    {
        return success(checkTypeService.selectCheckTypeByTypeId(typeId));
    }

    /**
     * 新增验证
     */
    @PreAuthorize("@ss.hasPermi('check:type:add')")
    @Log(title = "验证", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckType checkType)
    {
        return toAjax(checkTypeService.insertCheckType(checkType));
    }

    /**
     * 修改验证
     */
    @PreAuthorize("@ss.hasPermi('check:type:edit')")
    @Log(title = "验证", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckType checkType)
    {
        return toAjax(checkTypeService.updateCheckType(checkType));
    }

    /**
     * 删除验证
     */
    @PreAuthorize("@ss.hasPermi('check:type:remove')")
    @Log(title = "验证", businessType = BusinessType.DELETE)
	@DeleteMapping("/{typeIds}")
    public AjaxResult remove(@PathVariable Long[] typeIds)
    {
        return toAjax(checkTypeService.deleteCheckTypeByTypeIds(typeIds));
    }
}
