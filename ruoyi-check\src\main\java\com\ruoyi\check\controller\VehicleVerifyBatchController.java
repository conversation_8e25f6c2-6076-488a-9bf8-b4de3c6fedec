package com.ruoyi.check.controller;

import com.ruoyi.check.service.IVehicleVerifyBatchService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 车辆验证批处理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/check/vehicle/batch")
public class VehicleVerifyBatchController extends BaseController {
    
    @Autowired
    private IVehicleVerifyBatchService vehicleVerifyBatchService;
    
    /**
     * 批量验证车辆行驶证真实性
     */
    @Anonymous
    @Log(title = "车辆行驶证真实性验证", businessType = BusinessType.EXPORT)
    @PostMapping("/verify/drivinglicense/authenticity")
    public AjaxResult batchVerifyVehicleDrivingLicense() {
        String result = vehicleVerifyBatchService.batchVerifyVehicleDrivingLicense();
        return AjaxResult.success(result);
    }
    
    /**
     * 批量验证车辆行驶证有效性
     */
    @Anonymous
    @Log(title = "车辆行驶证有效性验证", businessType = BusinessType.EXPORT)
    @PostMapping("/verify/drivinglicense/validity")
    public AjaxResult batchVerifyVehicleDrivingLicenseValidity() {
        String result = vehicleVerifyBatchService.batchVerifyVehicleDrivingLicenseValidity();
        return AjaxResult.success(result);
    }
    
    /**
     * 批量验证车辆道路运输证真实性
     */
    @Anonymous
    @Log(title = "车辆道路运输证真实性验证", businessType = BusinessType.EXPORT)
    @PostMapping("/verify/roadtransport/authenticity")
    public AjaxResult batchVerifyVehicleRoadTransport() {
        String result = vehicleVerifyBatchService.batchVerifyVehicleRoadTransport();
        return AjaxResult.success(result);
    }
    
    /**
     * 批量验证车辆道路运输证有效性
     */
    @Anonymous
    @Log(title = "车辆道路运输证有效性验证", businessType = BusinessType.EXPORT)
    @PostMapping("/verify/roadtransport/validity")
    public AjaxResult batchVerifyVehicleRoadTransportValidity() {
        String result = vehicleVerifyBatchService.batchVerifyVehicleRoadTransportValidity();
        return AjaxResult.success(result);
    }
    
    /**
     * 批量验证车辆所有证件（行驶证和道路运输证的真实性和有效性）
     */
    @Anonymous
    @Log(title = "车辆所有证件验证", businessType = BusinessType.EXPORT)
    @PostMapping("/verify/all")
    public AjaxResult batchVerifyVehicleAll() {
        String result = vehicleVerifyBatchService.batchVerifyVehicleAll();
        return AjaxResult.success(result);
    }
    
    /**
     * 获取批处理任务状态
     */
    @Anonymous
    @GetMapping("/status")
    public AjaxResult getBatchTaskStatus() {
        String status = vehicleVerifyBatchService.getBatchTaskStatus();
        return AjaxResult.success(status);
    }
} 