package com.ruoyi.check.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.check.mapper.CheckTypeMapper;
import com.ruoyi.check.domain.CheckType;
import com.ruoyi.check.service.ICheckTypeService;

/**
 * 验证Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class CheckTypeServiceImpl implements ICheckTypeService 
{
    @Autowired
    private CheckTypeMapper checkTypeMapper;

    /**
     * 查询验证
     * 
     * @param typeId 验证主键
     * @return 验证
     */
    @Override
    public CheckType selectCheckTypeByTypeId(Long typeId)
    {
        return checkTypeMapper.selectCheckTypeByTypeId(typeId);
    }

    /**
     * 查询验证列表
     * 
     * @param checkType 验证
     * @return 验证
     */
    @Override
    public List<CheckType> selectCheckTypeList(CheckType checkType)
    {
        return checkTypeMapper.selectCheckTypeList(checkType);
    }

    /**
     * 新增验证
     * 
     * @param checkType 验证
     * @return 结果
     */
    @Override
    public int insertCheckType(CheckType checkType)
    {
        checkType.setCreateTime(DateUtils.getNowDate());
        return checkTypeMapper.insertCheckType(checkType);
    }

    /**
     * 修改验证
     * 
     * @param checkType 验证
     * @return 结果
     */
    @Override
    public int updateCheckType(CheckType checkType)
    {
        checkType.setUpdateTime(DateUtils.getNowDate());
        return checkTypeMapper.updateCheckType(checkType);
    }

    /**
     * 批量删除验证
     * 
     * @param typeIds 需要删除的验证主键
     * @return 结果
     */
    @Override
    public int deleteCheckTypeByTypeIds(Long[] typeIds)
    {
        return checkTypeMapper.deleteCheckTypeByTypeIds(typeIds);
    }

    /**
     * 删除验证信息
     * 
     * @param typeId 验证主键
     * @return 结果
     */
    @Override
    public int deleteCheckTypeByTypeId(Long typeId)
    {
        return checkTypeMapper.deleteCheckTypeByTypeId(typeId);
    }
}
