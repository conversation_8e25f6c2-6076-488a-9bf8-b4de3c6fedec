package com.ruoyi.check.controller;

import com.ruoyi.check.domain.*;
import com.ruoyi.check.service.VehicleTrackService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 车辆轨迹控制器
 */
@Api(tags = "货达Mock服务-车辆轨迹接口", description = "提供车辆轨迹查询服务")
@RestController
@RequestMapping("/api/v1/huoda/vehicle/track")
public class VehicleTrackController extends BaseController {
    
    @Autowired
    private VehicleTrackService vehicleTrackService;
    
    /**
     * 车辆历史轨迹查询接口
     */
    @ApiOperation(value = "车辆历史轨迹查询", 
                  notes = "查询指定时间段内车辆的历史轨迹信息。\n\n" +
                         "请求示例：\n" +
                         "```json\n" +
                         "{\n" +
                         "  \"vehicles\": [\n" +
                         "    {\n" +
                         "      \"plateNumber\": \"陕A12345\",\n" +
                         "      \"loadTime\": \"2024-01-01 08:00:00\",\n" +
                         "      \"arriveTime\": \"2024-01-01 18:00:00\"\n" +
                         "    }\n" +
                         "  ],\n" +
                         "  \"startTime\": \"2024-01-01 00:00:00\",\n" +
                         "  \"endTime\": \"2024-01-01 23:59:59\"\n" +
                         "}\n" +
                         "```")
    @ApiImplicitParam(name = "request", value = "车辆轨迹查询请求参数", required = true, dataTypeClass = VehicleTrackHistoryRequest.class)
    @Anonymous
    @PostMapping("/history")
    public AjaxResult queryTrackHistory(@RequestBody VehicleTrackHistoryRequest request) {
        VehicleTrackResponseData responseData = vehicleTrackService.queryTrackHistory(request);
        return AjaxResult.success("轨迹查询成功", responseData);
    }
} 