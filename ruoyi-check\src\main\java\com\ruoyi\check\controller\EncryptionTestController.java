//package com.ruoyi.check.controller;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.ruoyi.check.service.ClientEncryptionService;
//import com.ruoyi.check.utils.AESUtils;
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.enums.BusinessType;
//
//import io.swagger.annotations.ApiOperation;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 加密传输测试控制器
// *
// * <AUTHOR>
// * @date 2025-01-20
// */
//@RestController
//@RequestMapping("/check/encryption/test")
//public class EncryptionTestController extends BaseController {
//
//    private static final Logger logger = LoggerFactory.getLogger(EncryptionTestController.class);
//
//    @Autowired
//    private ClientEncryptionService clientEncryptionService;
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    private final ObjectMapper objectMapper = new ObjectMapper();
//
//    @Value("${driver.verify.use.encryption:true}")
//    private boolean useEncryption;
//
//    /**
//     * 测试手机号验证加密传输
//     */
//    @ApiOperation(value = "测试手机号验证加密传输", notes = "测试手机号验证加密传输")
//    @PreAuthorize("@ss.hasPermi('check:encryption:test')")
//    @Log(title = "加密传输测试", businessType = BusinessType.OTHER)
//    @PostMapping("/phone")
//    public AjaxResult testPhoneVerifyEncryption() {
//        try {
//            // 构建测试数据
//            Map<String, Object> request = new HashMap<>();
//            List<Map<String, Object>> phones = new ArrayList<>();
//
//            Map<String, Object> phoneData = new HashMap<>();
//            phoneData.put("phone", "***********");
//            phoneData.put("name", "张三");
//            phoneData.put("idCard", "610528199001010001");
//            phones.add(phoneData);
//
//            phoneData = new HashMap<>();
//            phoneData.put("phone", "***********");
//            phoneData.put("name", "李四");
//            phoneData.put("idCard", "610528199002020002");
//            phones.add(phoneData);
//
//            request.put("phones", phones);
//
//            // 发送加密请求
//            String url = "http://localhost:8080/api/v1/huoda/secure/driver/verify/phone";
//            Object response = clientEncryptionService.sendEncryptedRequest(url, request, Object.class);
//
//            return AjaxResult.success("加密传输测试成功", response);
//
//        } catch (Exception e) {
//            logger.error("加密传输测试失败", e);
//            return AjaxResult.error("加密传输测试失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 测试驾驶证验证加密传输
//     */
//    @ApiOperation(value = "测试驾驶证验证加密传输", notes = "测试驾驶证验证加密传输")
//    @PreAuthorize("@ss.hasPermi('check:encryption:test')")
//    @Log(title = "加密传输测试", businessType = BusinessType.OTHER)
//    @PostMapping("/license")
//    public AjaxResult testLicenseVerifyEncryption() {
//        try {
//            // 构建测试数据
//            Map<String, Object> request = new HashMap<>();
//            request.put("verifyType", "authenticity");
//
//            List<Map<String, Object>> licenses = new ArrayList<>();
//
//            Map<String, Object> licenseData = new HashMap<>();
//            licenseData.put("idCard", "6105281122111138");
//            licenseData.put("name", "李四");
//            licenseData.put("licenseNumber", "610528199909191102");
//            licenseData.put("licenseType", "B2");
//            licenseData.put("validStartDate", "2020-01-01");
//            licenseData.put("validEndDate", "2026-01-01");
//            licenseData.put("archiveNumber", "610528001234");
//            licenses.add(licenseData);
//
//            request.put("licenses", licenses);
//
//            // 发送加密请求
//            String url = "http://localhost:8080/api/v1/huoda/secure/driver/verify/drivinglicense";
//            Object response = clientEncryptionService.sendEncryptedRequest(url, request, Object.class);
//
//            return AjaxResult.success("驾驶证加密传输测试成功", response);
//
//        } catch (Exception e) {
//            logger.error("驾驶证加密传输测试失败", e);
//            return AjaxResult.error("驾驶证加密传输测试失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 重置服务端公钥缓存
//     */
//    @ApiOperation(value = "重置服务端公钥缓存", notes = "重置服务端公钥缓存")
//    @PreAuthorize("@ss.hasPermi('check:encryption:reset')")
//    @Log(title = "重置公钥缓存", businessType = BusinessType.OTHER)
//    @PostMapping("/reset-key")
//    public AjaxResult resetServerPublicKey() {
//        try {
//            clientEncryptionService.resetServerPublicKey();
//            return AjaxResult.success("公钥缓存重置成功");
//        } catch (Exception e) {
//            logger.error("重置公钥缓存失败", e);
//            return AjaxResult.error("重置公钥缓存失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取加密传输状态
//     */
//    @ApiOperation(value = "获取加密传输状态", notes = "获取加密传输状态")
//    @PreAuthorize("@ss.hasPermi('check:encryption:status')")
//    @GetMapping("/status")
//    public AjaxResult getEncryptionStatus() {
//        Map<String, Object> status = new HashMap<>();
//        status.put("encryptionEnabled", true);
//        status.put("algorithm", "AES-256-CBC + RSA-2048");
//        status.put("keyExchange", "RSA公钥加密AES密钥");
//        status.put("integrity", "SHA-256哈希校验");
//        status.put("antiReplay", "时间戳验证（5分钟窗口）");
//
//        return AjaxResult.success("获取加密状态成功", status);
//    }
//
//    /**
//     * AES加密解密测试
//     */
//    @ApiOperation(value = "AES加密解密测试", notes = "测试AES加密和解密功能")
//    @PostMapping("/aes")
//    public AjaxResult testAESEncryption(@RequestBody Map<String, Object> testData) {
//        try {
//            logger.info("开始AES加密测试");
//
//            // 1. 序列化测试数据
//            String originalData = objectMapper.writeValueAsString(testData);
//            logger.info("原始数据: {}", originalData);
//
//            // 2. AES加密
//            String encryptedData = AESUtils.encrypt(originalData);
//            logger.info("加密后数据: {}", encryptedData);
//
//            // 3. AES解密
//            String decryptedData = AESUtils.decrypt(encryptedData);
//            logger.info("解密后数据: {}", decryptedData);
//
//            // 4. 验证数据一致性
//            boolean isConsistent = originalData.equals(decryptedData);
//
//            Map<String, Object> result = new HashMap<>();
//            result.put("originalData", originalData);
//            result.put("encryptedData", encryptedData);
//            result.put("decryptedData", decryptedData);
//            result.put("isConsistent", isConsistent);
//            result.put("useEncryption", useEncryption);
//
//            return success("AES加密测试完成", result);
//
//        } catch (Exception e) {
//            logger.error("AES加密测试失败", e);
//            return error("AES加密测试失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 测试调用加密接口
//     */
//    @ApiOperation(value = "测试调用加密接口", notes = "测试调用ruoyi-mock的加密接口")
//    @PostMapping("/call-encrypted-api")
//    public AjaxResult testCallEncryptedApi(@RequestBody Map<String, Object> testData) {
//        try {
//            logger.info("开始测试调用加密接口");
//
//            // 1. 序列化请求数据
//            String requestJson = objectMapper.writeValueAsString(testData);
//            logger.info("请求数据: {}", requestJson);
//
//            // 2. 加密请求数据
//            String encryptedData = AESUtils.encrypt(requestJson);
//
//            // 3. 构建加密请求包装
//            Map<String, Object> encryptedRequest = new HashMap<>();
//            encryptedRequest.put("encryptedData", encryptedData);
//            encryptedRequest.put("timestamp", System.currentTimeMillis());
//
//            // 4. 设置HTTP头
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//
//            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);
//
//            // 5. 调用加密接口（这里使用手机号验证接口作为示例）
//            String encryptedApiUrl = "http://localhost:8080/api/v1/huoda/encrypted/driver/verify/phone";
//            ResponseEntity<Map> response = restTemplate.postForEntity(encryptedApiUrl, entity, Map.class);
//
//            logger.info("接口响应: {}", response.getBody());
//
//            Map<String, Object> result = new HashMap<>();
//            result.put("requestData", testData);
//            result.put("encryptedRequest", encryptedRequest);
//            result.put("response", response.getBody());
//
//            return success("加密接口调用测试完成", result);
//
//        } catch (Exception e) {
//            logger.error("加密接口调用测试失败", e);
//            return error("加密接口调用测试失败: " + e.getMessage());
//        }
//    }
//}