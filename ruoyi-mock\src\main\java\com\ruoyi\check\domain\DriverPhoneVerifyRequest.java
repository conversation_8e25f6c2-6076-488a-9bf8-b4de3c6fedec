package com.ruoyi.check.domain;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 司机手机号验证请求对象
 */
@ApiModel(value = "DriverPhoneVerifyRequest", description = "司机手机号验证请求")
public class DriverPhoneVerifyRequest {
    
    @ApiModelProperty("手机号验证数据列表")
    private List<DriverPhoneData> phones;
    
    public List<DriverPhoneData> getPhones() {
        return phones;
    }
    
    public void setPhones(List<DriverPhoneData> phones) {
        this.phones = phones;
    }
    
    @ApiModel(value = "DriverPhoneData", description = "司机手机号验证数据")
    public static class DriverPhoneData {
        @ApiModelProperty("手机号码（密文）")
        private String phone;    // 手机号码（密文）
        
        @ApiModelProperty("姓名（明文）")
        private String name;     // 姓名（明文）
        
        @ApiModelProperty("身份证号（密文）")
        private String idCard;   // 身份证号（密文）
        
        public String getPhone() {
            return phone;
        }
        
        public void setPhone(String phone) {
            this.phone = phone;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
    }
} 