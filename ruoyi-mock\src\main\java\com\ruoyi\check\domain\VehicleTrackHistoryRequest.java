package com.ruoyi.check.domain;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车辆轨迹查询请求对象
 */
@ApiModel(value = "VehicleTrackHistoryRequest", description = "车辆轨迹查询请求")
public class VehicleTrackHistoryRequest {
    
    @ApiModelProperty("车辆轨迹数据列表")
    private List<VehicleTrackData> vehicles;
    
    @ApiModelProperty("开始时间")
    private String startTime;  // 开始时间
    
    @ApiModelProperty("结束时间")
    private String endTime;    // 结束时间
    
    public List<VehicleTrackData> getVehicles() {
        return vehicles;
    }
    
    public void setVehicles(List<VehicleTrackData> vehicles) {
        this.vehicles = vehicles;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    @ApiModel(value = "VehicleTrackData", description = "车辆轨迹数据")
    public static class VehicleTrackData {
        @ApiModelProperty("车牌号")
        private String plateNumber;  // 车牌号
        
        @ApiModelProperty("装车时间")
        private String loadTime;     // 装车时间
        
        @ApiModelProperty("运抵时间")
        private String arriveTime;   // 运抵时间
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getLoadTime() {
            return loadTime;
        }
        
        public void setLoadTime(String loadTime) {
            this.loadTime = loadTime;
        }
        
        public String getArriveTime() {
            return arriveTime;
        }
        
        public void setArriveTime(String arriveTime) {
            this.arriveTime = arriveTime;
        }
    }
} 