介绍：
    技术介绍：这是一个ruoyi-vue的项目，框架是springboot和vue
    背景介绍：要做的是一个司机和车辆验证的需求。我们是甲方，我们有一些
    信息想调用乙方的接口去验证。验真的信息如下：
    1．司机基础数据验证
       (1)手机号码验证：验证手机号及手机号开卡人一致性。
       (2)身份证验证：验证身份证号、姓名的一致性正确性。
       (3)驾驶证验证：验证驾驶证的真实性，校验项包括姓名、身份证号/驾驶证号码、驾照类型（如B2）、驾驶证有效性期、驾驶证档案编号。
       (4)从业资格证：验证从业资格证的真实性，验证的数据项包括姓名、从业资格证编号、从业资格证有效性。
    2. 车辆基础数据验证
       (1)行驶证验证：验证行驶证的真实性及有效性。验证的信息包括车牌号、车辆识别代码、车辆类型、所有人及证件有效性。
       (2)车辆道路运输证：验证车辆道路运输证的真实性及有效性。通过车辆道路运输证编号、车牌号、业户名称，验证车辆道路运输证的真实性及有效性。

    这两个需求设计到的模块是
    ruoyi-check: 这个模块是我们写的，我们在这个模块中要进行“司机基础数据验证”和“车辆基础数据验证”
    我们在这个模块中会进行远程调用，调用乙方写的代码。这里的乙方还没有实现，我专门模拟了一个ruoyi-mock
    这个模块，这个模块是用来模拟乙方的，等乙方实现完后，调用的url会变成真实的乙方的url。
    调用的思路是我们从数据库中查询出“车辆表”和“司机表”，然后把这些数据批量调用ruoyi-mock的验证方法。
    CheckDriverController和CheckVehicleController 分别对应“司机表”和“车辆表”的增删改查
    DriverVerifyBatchController和VehicleVerifyBatchController 分别对应“司机基础数据验证”和“车辆基础数据验证”

    ruoyi-mock：这个模块是我们写的模拟乙方的代码，以后乙方写完这个模块就不用了。



要求：
   1 你只需要关注ruoyi-check和ruoyi-mock这两个模块，其他模块暂时不关心。
     比如说这里调用 “司机验证接口”，
     请求地址如下：“http://localhost:8973/api/v1/huoda/driver/verify/drivinglicense”
     返回信息如下：
     {
        "msg": "验证成功",
        "code": 200,
        "data": {
            "verifyType": "authenticity",
            "total": 1,
            "results": [
            {
                "phone": null,
                "idCard": "610528199751208101",
                "name": "李四",
                "licenseNumber": "610528199909191102",
                "qualificationNumber": null,
                "plateNumber": null,
                "transportLicenseNumber": null,
                "owner": null,
                "formatValid": null,
                "verifyResult": "不一致",
                "message": "驾驶证信息与身份证不匹配",
                "correctInfo": null
            }
            ]
        }
    }

    这里的"msg": "验证成功",  统一修改为如果 code是200，msg显示“请求处理成功”。
    同理，把其他所有的验证的也按照这么修改。

 2   看他返回值是"verifyResult": "不一致"。 这里面少了个错误code码，请帮我添加上名字叫"resultCode"。规则如下：

     如果"verifyResult": "不一致", 那么"resultCode"是1
     如果"verifyResult": "失效", 那么"resultCode"是2
     如果"verifyResult": "不存在", 那么"resultCode"是3


  3  手机号码批量验证接口
     check模块去访问mock模块的url如下：/api[表情]1/{carrier}/driver[表情]erify/phone
     目前请求参数如下：
     {
        "phones": [
            {
                "phone": "13800138000",
                "idCard": "6105281122111138",
                "name": "李四"
            },
            {
                "phone": "13900139000",
                "idCard": "6105291122222222",
                "name": "张三"
            }
        ]
     }
     修改为： 入参去掉"idCard"
     {
        "phones": [
            {
                "phone": "13800138000",
                "name": "李四"
            },
            {
                "phone": "13900139000",
                "name": "张三"
            }
        ]
     }

4 访问url如下：
  /api[表情]1/huoda/driver[表情]erify/idcard
  这个接口中，返回值如下
  {
  "msg": "请求处理成功",
  "code": 200,
  "data": {
    "verifyType": "string",
    "total": 1,
    "results": [
      {
        "phone": null,
        "idCard": "610528199840136716",
        "name": "string",
        "licenseNumber": null,
        "qualificationNumber": null,
        "plateNumber": null,
        "transportLicenseNumber": null,
        "owner": null,
        "formatValid": true,
        "verifyResult": "不存在",
        "message": "身份证不存在",
        "resultCode": 3,
        "correctInfo": null
      }
    ]
  }
}
请去掉formatValid这个字段




5 身份证批量验证接口

/api[表情]1/huoda/driver[表情]erify/drivinglicense
目前如下如下示例如下：

{
  "verifyType": "authenticity",
  "licenses": [
    {
      "idCard": "6105281122111138",
      "name": "李四",
      "licenseNumber": "610528199909191102",
      "licenseType": "B2",
      "validStartDate": "2020-01-01",
      "validEndDate": "2026-01-01",
      "archiveNumber": "610528001234"
    },
    {
      "idCard": "6105291122222222",
      "name": "张三",
      "licenseNumber": "610529199909222222",
      "licenseType": "B2",
      "validStartDate": "2021-01-01",
      "validEndDate": "2027-01-01",
      "archiveNumber": "610529001235"
    }
  ]
}
现在修改请求参数不需要带 "validStartDate"和"validEndDate"这两个字段。



6

我的代码回滚了，重新帮我实现一下。

ruoyi-admin是一个聚合项目，他聚合了ruoyi-check和ruoyi-mock。现在的框架架构我觉得可以的
你只需要把配置文件的东西放到ruoyi-admin下面就可以了，不要单位在ruoyi-check和ruoyi-mock创建application等配置文件。

目前在ruoyi-check中调用ruoyi-mock进行验证的时候，我需要将我的消息体
进行加密。
请你给我调用ruoyi-mock进行验证的时候，消息体加密。
要求使用aes加密算法。 然后在ruoyi-mock接收到调用请求后进行解密。
不要用拦截器，过滤器等等，你就每个方法加密的写。