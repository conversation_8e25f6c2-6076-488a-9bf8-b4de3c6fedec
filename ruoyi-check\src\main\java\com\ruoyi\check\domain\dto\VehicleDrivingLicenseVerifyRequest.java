package com.ruoyi.check.domain.dto;

import java.util.List;

/**
 * 车辆行驶证验证请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class VehicleDrivingLicenseVerifyRequest {
    
    private String verifyType;  // 验证类型：authenticity（真实性）| validity（有效性）
    private List<VehicleData> vehicles;
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<VehicleData> getVehicles() {
        return vehicles;
    }
    
    public void setVehicles(List<VehicleData> vehicles) {
        this.vehicles = vehicles;
    }
    
    public static class VehicleData {
        private String plateNumber;                    // 车牌号
        private String vehicleIdentificationCode;      // 车辆识别代码
        private String vehicleType;                    // 车辆类型
        private String owner;                          // 所有人
        private String validEndDate;                   // 有效期结束日期
        
        public VehicleData() {}
        
        public VehicleData(String plateNumber, String vehicleIdentificationCode, String vehicleType, 
                          String owner, String validEndDate) {
            this.plateNumber = plateNumber;
            this.vehicleIdentificationCode = vehicleIdentificationCode;
            this.vehicleType = vehicleType;
            this.owner = owner;
            this.validEndDate = validEndDate;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getVehicleIdentificationCode() {
            return vehicleIdentificationCode;
        }
        
        public void setVehicleIdentificationCode(String vehicleIdentificationCode) {
            this.vehicleIdentificationCode = vehicleIdentificationCode;
        }
        
        public String getVehicleType() {
            return vehicleType;
        }
        
        public void setVehicleType(String vehicleType) {
            this.vehicleType = vehicleType;
        }
        
        public String getOwner() {
            return owner;
        }
        
        public void setOwner(String owner) {
            this.owner = owner;
        }
        
        public String getValidEndDate() {
            return validEndDate;
        }
        
        public void setValidEndDate(String validEndDate) {
            this.validEndDate = validEndDate;
        }
    }
}
