package com.ruoyi.check.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 加密请求包装类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@ApiModel(description = "加密请求数据")
public class EncryptedRequest {
    
    @ApiModelProperty(value = "加密的AES密钥（使用RSA公钥加密）", required = true)
    private String encryptedAESKey;
    
    @ApiModelProperty(value = "AES加密的初始化向量（IV）", required = true)
    private String iv;
    
    @ApiModelProperty(value = "加密的请求数据（使用AES加密）", required = true)
    private String encryptedData;
    
    @ApiModelProperty(value = "数据完整性校验哈希值", required = true)
    private String dataHash;
    
    @ApiModelProperty(value = "时间戳（防重放攻击）", required = true)
    private Long timestamp;
    
    public EncryptedRequest() {}
    
    public EncryptedRequest(String encryptedAESKey, String iv, String encryptedData, String dataHash, Long timestamp) {
        this.encryptedAESKey = encryptedAESKey;
        this.iv = iv;
        this.encryptedData = encryptedData;
        this.dataHash = dataHash;
        this.timestamp = timestamp;
    }
    
    public String getEncryptedAESKey() {
        return encryptedAESKey;
    }
    
    public void setEncryptedAESKey(String encryptedAESKey) {
        this.encryptedAESKey = encryptedAESKey;
    }
    
    public String getIv() {
        return iv;
    }
    
    public void setIv(String iv) {
        this.iv = iv;
    }
    
    public String getEncryptedData() {
        return encryptedData;
    }
    
    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }
    
    public String getDataHash() {
        return dataHash;
    }
    
    public void setDataHash(String dataHash) {
        this.dataHash = dataHash;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
} 