package com.ruoyi.check.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.check.domain.CheckDriver;
import com.ruoyi.check.domain.CheckType;
import com.ruoyi.check.domain.dto.PhoneVerifyRequest;
import com.ruoyi.check.domain.dto.PhoneVerifyResponse;
import com.ruoyi.check.domain.dto.IdCardVerifyRequest;
import com.ruoyi.check.domain.dto.IdCardVerifyResponse;
import com.ruoyi.check.domain.dto.DrivingLicenseVerifyRequest;
import com.ruoyi.check.domain.dto.DrivingLicenseVerifyResponse;
import com.ruoyi.check.domain.dto.QualificationVerifyRequest;
import com.ruoyi.check.domain.dto.QualificationVerifyResponse;
import com.ruoyi.check.service.ICheckDriverService;
import com.ruoyi.check.service.ICheckTypeService;
import com.ruoyi.check.service.IDriverVerifyBatchService;
import com.ruoyi.check.utils.AESUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 司机验证批处理服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class DriverVerifyBatchServiceImpl implements IDriverVerifyBatchService {
    
    private static final Logger logger = LoggerFactory.getLogger(DriverVerifyBatchServiceImpl.class);
    
    @Autowired
    private ICheckDriverService checkDriverService;
    
    @Autowired
    private ICheckTypeService checkTypeService;
    
    @Autowired
    private RestTemplate restTemplate;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 每批处理的数量
    private static final int BATCH_SIZE = 200;
    
    // 批次间隔时间（毫秒）
    private static final long BATCH_INTERVAL = 2000;
    
    // 手机号验证接口URL
    @Value("${driver.verify.phone.url:http://localhost:8080/api/v1/huoda/driver/verify/phone}")
    private String phoneVerifyUrl;
    
    // 身份证验证接口URL
    @Value("${driver.verify.idcard.url:http://localhost:8080/api/v1/huoda/driver/verify/idcard}")
    private String idCardVerifyUrl;
    
    // 驾驶证验证接口URL
    @Value("${driver.verify.license.url:http://localhost:8080/api/v1/huoda/driver/verify/drivinglicense}")
    private String licenseVerifyUrl;
    
    @Value("${driver.verify.qualification.url:http://localhost:8080/api/v1/huoda/driver/verify/qualification}")
    private String qualificationVerifyUrl;
    
    // 是否使用加密传输
    @Value("${driver.verify.use.encryption:true}")
    private boolean useEncryption;
    
    // 加密接口URL
    @Value("${driver.verify.encrypted.phone.url:http://localhost:8080/api/v1/huoda/encrypted/driver/verify/phone}")
    private String encryptedPhoneVerifyUrl;
    
    @Value("${driver.verify.encrypted.idcard.url:http://localhost:8080/api/v1/huoda/encrypted/driver/verify/idcard}")
    private String encryptedIdCardVerifyUrl;
    
    @Value("${driver.verify.encrypted.license.url:http://localhost:8080/api/v1/huoda/encrypted/driver/verify/drivinglicense}")
    private String encryptedLicenseVerifyUrl;
    
    @Value("${driver.verify.encrypted.qualification.url:http://localhost:8080/api/v1/huoda/encrypted/driver/verify/qualification}")
    private String encryptedQualificationVerifyUrl;
    
    // 任务状态
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong totalCount = new AtomicLong(0);
    private String lastMessage = "未开始执行";
    
    @Override
    public String batchVerifyDriverPhones() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("phone")).start();
        
        return "手机号批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyDriverIdCards() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("idcard")).start();
        
        return "身份证真实性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyDriverIdCardValidity() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("idcard_validity")).start();
        
        return "身份证有效性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyDriverLicense() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("license")).start();
        
        return "驾驶证真实性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyDriverLicenseValidity() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("license_validity")).start();
        
        return "驾驶证有效性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyDriverQualification() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("qualification")).start();
        
        return "从业资格证真实性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyDriverQualificationValidity() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("qualification_validity")).start();
        
        return "从业资格证有效性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyDriverAll() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("all")).start();
        
        return "完整验证批处理任务已启动（手机号+身份证真实性+身份证有效性+驾驶证真实性+驾驶证有效性+从业资格证真实性+从业资格证有效性），正在后台执行";
    }
    
    private void executeTask(String verifyType) {
        try {
            isRunning.set(true);
            processedCount.set(0);
            
            // 获取总数据量
            long total = checkDriverService.countCheckDrivers();
            totalCount.set(total);
            
            logger.info("开始批量验证司机信息，验证类型: {}, 总数据量: {}", verifyType, total);
            lastMessage = String.format("开始批量验证，验证类型: %s, 总数据量: %d", verifyType, total);
            
            long offset = 0;
            int successCount = 0;
            int errorCount = 0;
            
            while (offset < total) {
                try {
                    // 分批查询司机数据
                    List<CheckDriver> drivers = checkDriverService.selectCheckDriverListByPage(offset, BATCH_SIZE);
                    
                    if (drivers.isEmpty()) {
                        break;
                    }
                    
                    logger.info("处理第 {} 批数据，数量: {}", (offset / BATCH_SIZE + 1), drivers.size());
                    
                    // 根据验证类型执行不同的验证流程
                    switch (verifyType) {
                        case "phone":
                            successCount += processPhoneVerification(drivers);
                            break;
                        case "idcard":
                            successCount += processIdCardVerification(drivers, "authenticity");
                            break;
                        case "idcard_validity":
                            successCount += processIdCardVerification(drivers, "validity");
                            break;
                        case "license":
                            successCount += processLicenseVerification(drivers, "authenticity");
                            break;
                        case "license_validity":
                            successCount += processLicenseVerification(drivers, "validity");
                            break;
                        case "qualification":
                            successCount += processQualificationVerification(drivers, "authenticity");
                            break;
                        case "qualification_validity":
                            successCount += processQualificationVerification(drivers, "validity");
                            break;
                        case "all":
                            successCount += processAllVerification(drivers);
                            break;
                    }
                    
                    processedCount.addAndGet(drivers.size());
                    offset += BATCH_SIZE;
                    
                    // 更新状态消息
                    lastMessage = String.format("已处理: %d/%d, 成功: %d, 失败: %d", 
                        processedCount.get(), total, successCount, errorCount);
                    
                    // 批次间隔
                    if (offset < total) {
                        Thread.sleep(BATCH_INTERVAL);
                    }
                    
                } catch (Exception e) {
                    logger.error("处理批次数据时发生错误", e);
                    errorCount += BATCH_SIZE;
                    offset += BATCH_SIZE;
                }
            }
            
            lastMessage = String.format("批处理完成！总处理: %d, 成功: %d, 失败: %d", 
                total, successCount, errorCount);
            logger.info(lastMessage);
            
        } catch (Exception e) {
            logger.error("批处理任务执行失败", e);
            lastMessage = "批处理任务执行失败: " + e.getMessage();
        } finally {
            isRunning.set(false);
        }
    }
    
    private int processPhoneVerification(List<CheckDriver> drivers) {
        // 调用手机号验证接口
        PhoneVerifyResponse response = callPhoneVerifyApi(drivers);
        
        // 对于加密接口，response.code为null是正常的，因为解密后的数据只包含VerifyResponseData字段
        if (response != null) {
            List<PhoneVerifyResponse.VerifyResult> results = null;
            
            // 判断是否为直接包含results字段的响应格式（加密接口返回的格式）
            if (response.getResults() != null) {
                // 直接包含results字段（来自加密接口）
                results = response.getResults();
                logger.info("处理加密接口返回的手机号验证结果，共 {} 条", results.size());
            } else if (response.getCode() != null && response.getCode() == 200 && response.getData() != null) {
                // 嵌套在data中的响应格式（非加密接口返回的格式）
                results = response.getData().getResults();
                logger.info("处理非加密接口返回的手机号验证结果，共 {} 条", results != null ? results.size() : 0);
            }
            
            if (results != null && !results.isEmpty()) {
                // 保存验证结果
                int saved = savePhoneVerifyResults(results);
                logger.info("成功保存 {} 条手机号验证结果", saved);
                return saved;
            } else {
                logger.warn("手机号验证接口返回空结果");
                return 0;
            }
        } else {
            logger.error("手机号验证接口调用失败，响应为null");
            return 0;
        }
    }
    
    private int processIdCardVerification(List<CheckDriver> drivers, String verifyType) {
        // 调用身份证验证接口
        IdCardVerifyResponse response = callIdCardVerifyApi(drivers, verifyType);
        
        if (response != null) {
            List<IdCardVerifyResponse.IdCardResult> results = null;
            
            // 判断是否为加密接口返回的数据结构（直接包含results字段）
            if (response.getResults() != null) {
                // 加密接口返回的数据结构
                results = response.getResults();
                logger.info("处理加密接口返回的身份证验证结果，共 {} 条", results.size());
            } else if (response.getCode() != null && response.getCode() == 200 && response.getData() != null) {
                // 非加密接口返回的数据结构
                results = response.getData().getResults();
                logger.info("处理非加密接口返回的身份证验证结果，共 {} 条", results != null ? results.size() : 0);
            }
            
            if (results != null && !results.isEmpty()) {
                // 保存验证结果
                int saved = saveIdCardVerifyResults(results, verifyType);
                logger.info("成功保存 {} 条身份证{}验证结果", saved, 
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return saved;
            } else {
                logger.warn("身份证{}验证接口返回空结果", 
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return 0;
            }
        } else {
            logger.error("身份证{}验证接口调用失败，响应为null", 
                "authenticity".equals(verifyType) ? "真实性" : "有效性");
            return 0;
        }
    }
    
    private int processAllVerification(List<CheckDriver> drivers) {
        int totalSaved = 0;
        
        // 先验证手机号
        totalSaved += processPhoneVerification(drivers);
        
        // 再验证身份证真实性
        totalSaved += processIdCardVerification(drivers, "authenticity");
        
        // 验证身份证有效性
        totalSaved += processIdCardVerification(drivers, "validity");
        
        // 验证驾驶证真实性
        totalSaved += processLicenseVerification(drivers, "authenticity");
        
        // 验证驾驶证有效性
        totalSaved += processLicenseVerification(drivers, "validity");
        
        // 验证从业资格证真实性
        totalSaved += processQualificationVerification(drivers, "authenticity");
        
        // 验证从业资格证有效性
        totalSaved += processQualificationVerification(drivers, "validity");
        
        return totalSaved;
    }
    
    private int processLicenseVerification(List<CheckDriver> drivers, String verifyType) {
        // 调用驾驶证验证接口
        DrivingLicenseVerifyResponse response = callLicenseVerifyApi(drivers, verifyType);

        if (response != null) {
            List<DrivingLicenseVerifyResponse.LicenseResult> results = null;

            // 判断是否为加密接口返回的数据结构（直接包含results字段）
            if (response.getResults() != null) {
                // 加密接口返回的数据结构
                results = response.getResults();
                logger.info("处理加密接口返回的驾驶证验证结果，共 {} 条", results.size());
            } else if (response.getCode() != null && response.getCode() == 200 && response.getData() != null) {
                // 非加密接口返回的数据结构
                results = response.getData().getResults();
                logger.info("处理非加密接口返回的驾驶证验证结果，共 {} 条", results != null ? results.size() : 0);
            }

            if (results != null && !results.isEmpty()) {
                // 保存验证结果
                int saved = saveLicenseVerifyResults(results, verifyType);
                logger.info("成功保存 {} 条驾驶证{}验证结果", saved,
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return saved;
            } else {
                logger.warn("驾驶证{}验证接口返回空结果",
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return 0;
            }
        } else {
            logger.error("驾驶证{}验证接口调用失败，响应为null",
                "authenticity".equals(verifyType) ? "真实性" : "有效性");
            return 0;
        }
    }
    
    private PhoneVerifyResponse callPhoneVerifyApi(List<CheckDriver> drivers) {
        try {
            // 构建请求数据
            PhoneVerifyRequest request = new PhoneVerifyRequest();
            List<PhoneVerifyRequest.PhoneData> phones = new ArrayList<>();
            
            for (CheckDriver driver : drivers) {
                if (StringUtils.isNotEmpty(driver.getTELEPHONE()) && 
                    StringUtils.isNotEmpty(driver.getNAME())) {
                    
                    PhoneVerifyRequest.PhoneData phoneData = new PhoneVerifyRequest.PhoneData(
                        driver.getTELEPHONE(),
                        driver.getNAME(),
                        driver.getIdNo()
                    );
                    phones.add(phoneData);
                }
            }
            
            if (phones.isEmpty()) {
                logger.warn("当前批次没有有效的手机号数据");
                return null;
            }
            
            request.setPhones(phones);
            
            // 根据配置选择加密或非加密调用
            if (useEncryption) {
                logger.info("使用加密方式调用手机号验证接口");
                try {
                    return callEncryptedPhoneVerifyApi(request);
                } catch (Exception e) {
                    logger.error("调用加密手机号验证接口失败", e);
                    return null;  // 返回null而不是抛出异常
                }
            } else {
                logger.info("使用非加密方式调用手机号验证接口");
                // 设置HTTP头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                
                HttpEntity<PhoneVerifyRequest> entity = new HttpEntity<>(request, headers);
                
                // 调用接口
                ResponseEntity<PhoneVerifyResponse> response = restTemplate.postForEntity(
                    phoneVerifyUrl, entity, PhoneVerifyResponse.class);
                
                return response.getBody();
            }
            
        } catch (Exception e) {
            logger.error("调用手机号验证接口失败", e);
            return null;
        }
    }
    
    private IdCardVerifyResponse callIdCardVerifyApi(List<CheckDriver> drivers, String verifyType) {
        try {
            // 构建请求数据
            IdCardVerifyRequest request = new IdCardVerifyRequest();
            request.setVerifyType(verifyType); // 设置验证类型（authenticity或validity）
            List<IdCardVerifyRequest.IdCardData> idCards = new ArrayList<>();
            
            for (CheckDriver driver : drivers) {
                if (StringUtils.isNotEmpty(driver.getIdNo()) && 
                    StringUtils.isNotEmpty(driver.getNAME())) {
                    
                    IdCardVerifyRequest.IdCardData idCardData = new IdCardVerifyRequest.IdCardData(
                        driver.getIdNo(),
                        driver.getNAME()
                    );
                    idCards.add(idCardData);
                }
            }
            
            if (idCards.isEmpty()) {
                logger.warn("当前批次没有有效的身份证数据");
                return null;
            }
            
            request.setIdCards(idCards);
            
            // 根据配置选择加密或非加密调用
            if (useEncryption) {
                logger.info("使用加密传输调用身份证验证接口");
                return callEncryptedIdCardVerifyApi(request);
            } else {
                logger.info("使用明文传输调用身份证验证接口");
                // 设置HTTP头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                
                HttpEntity<IdCardVerifyRequest> entity = new HttpEntity<>(request, headers);
                
                // 调用接口
                ResponseEntity<IdCardVerifyResponse> response = restTemplate.postForEntity(
                    idCardVerifyUrl, entity, IdCardVerifyResponse.class);
                
                return response.getBody();
            }
            
        } catch (Exception e) {
            logger.error("调用身份证验证接口失败", e);
            return null;
        }
    }
    
    private DrivingLicenseVerifyResponse callLicenseVerifyApi(List<CheckDriver> drivers, String verifyType) {
        try {
            // 构建请求数据
            DrivingLicenseVerifyRequest request = new DrivingLicenseVerifyRequest();
            request.setVerifyType(verifyType); // 设置验证类型（authenticity或validity）
            List<DrivingLicenseVerifyRequest.LicenseData> licenses = new ArrayList<>();

            for (CheckDriver driver : drivers) {
                if (StringUtils.isNotEmpty(driver.getIdNo()) &&
                    StringUtils.isNotEmpty(driver.getNAME()) &&
                    StringUtils.isNotEmpty(driver.getLicenseNo())) {

                    // 格式化日期
                    String validStartDate = driver.getLicenseValidFrom() != null ?
                        new java.text.SimpleDateFormat("yyyy-MM-dd").format(driver.getLicenseValidFrom()) : null;
                    String validEndDate = driver.getLicenseValidTo() != null ?
                        new java.text.SimpleDateFormat("yyyy-MM-dd").format(driver.getLicenseValidTo()) : null;

                    DrivingLicenseVerifyRequest.LicenseData licenseData = new DrivingLicenseVerifyRequest.LicenseData(
                        driver.getIdNo(),
                        driver.getNAME(),
                        driver.getLicenseNo(),
                        driver.getLicenseClass(),
                        validStartDate,
                        validEndDate,
                        driver.getLicenseFileNo()
                    );
                    licenses.add(licenseData);
                }
            }

            if (licenses.isEmpty()) {
                logger.warn("当前批次没有有效的驾驶证数据");
                return null;
            }

            request.setLicenses(licenses);

            // 根据配置选择加密或非加密调用
            if (useEncryption) {
                logger.info("使用加密方式调用驾驶证验证接口");
                try {
                    return callEncryptedLicenseVerifyApi(request);
                } catch (Exception e) {
                    logger.error("调用加密驾驶证验证接口失败", e);
                    return null;  // 返回null而不是抛出异常
                }
            } else {
                logger.info("使用非加密方式调用驾驶证验证接口");
                // 设置HTTP头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);

                HttpEntity<DrivingLicenseVerifyRequest> entity = new HttpEntity<>(request, headers);

                // 调用接口
                ResponseEntity<DrivingLicenseVerifyResponse> response = restTemplate.postForEntity(
                    licenseVerifyUrl, entity, DrivingLicenseVerifyResponse.class);

                return response.getBody();
            }

        } catch (Exception e) {
            logger.error("调用驾驶证验证接口失败", e);
            return null;
        }
    }
    
    private int savePhoneVerifyResults(List<PhoneVerifyResponse.VerifyResult> results) {
        if (results == null || results.isEmpty()) {
            return 0;
        }
        
        int savedCount = 0;
        
        for (PhoneVerifyResponse.VerifyResult result : results) {
            try {
                CheckType checkType = new CheckType();
                
                // 根据需求设置字段值
                checkType.setCheckStatus("一致性验证");  // 验证类型：一致性验证
                checkType.setDataStatus("手机号码");     // 数据类型：手机号码
                checkType.setCheckData(result.getPhone());  // 验证的数据（手机号）
                checkType.setCheckName(result.getName());   // 名称
                
                // hd_result: 将verifyResult转换为对应的结果
                String hdResult = convertVerifyResult(result.getVerifyResult());
                checkType.setHdResult(hdResult);
                
                // detail: 只有不通过的情况下才存放message
                if ("不通过".equals(hdResult)) {
                    checkType.setDetail(result.getMessage());
                } else {
                    checkType.setDetail(null);  // 通过和不存在的情况不存放detail
                }
                
                // 设置创建时间
                checkType.setCreateTime(DateUtils.getNowDate());
                
                // 保存到数据库
                checkTypeService.insertCheckType(checkType);
                savedCount++;
                
            } catch (Exception e) {
                logger.error("保存手机号验证结果失败: {}", result, e);
            }
        }
        
        return savedCount;
    }
    
    private int saveIdCardVerifyResults(List<IdCardVerifyResponse.IdCardResult> results, String verifyType) {
        if (results == null || results.isEmpty()) {
            return 0;
        }
        
        int savedCount = 0;
        
        for (IdCardVerifyResponse.IdCardResult result : results) {
            try {
                CheckType checkType = new CheckType();
                
                // 根据验证类型设置字段值
                String checkStatus = "authenticity".equals(verifyType) ? "真实性验证" : "有效性验证";
                checkType.setCheckStatus(checkStatus);  // 验证类型
                checkType.setDataStatus("身份证");      // 数据类型：身份证
                checkType.setCheckData(result.getIdCard());  // 验证的数据（身份证号）
                checkType.setCheckName(result.getName());    // 名称
                
                // hd_result: 将verifyResult转换为对应的结果
                String hdResult = convertVerifyResult(result.getVerifyResult());
                checkType.setHdResult(hdResult);
                
                // detail: 只有不通过的情况下才存放message
                if ("不通过".equals(hdResult)) {
                    checkType.setDetail(result.getMessage());
                } else {
                    checkType.setDetail(null);  // 通过和不存在的情况不存放detail
                }
                
                // 设置创建时间
                checkType.setCreateTime(DateUtils.getNowDate());
                
                // 保存到数据库
                checkTypeService.insertCheckType(checkType);
                savedCount++;
                
            } catch (Exception e) {
                logger.error("保存身份证验证结果失败: {}", result, e);
            }
        }
        
        return savedCount;
    }
    
    private int saveLicenseVerifyResults(List<DrivingLicenseVerifyResponse.LicenseResult> results, String verifyType) {
        if (results == null || results.isEmpty()) {
            return 0;
        }
        
        int savedCount = 0;
        
        for (DrivingLicenseVerifyResponse.LicenseResult result : results) {
            try {
                CheckType checkType = new CheckType();
                
                // 根据验证类型设置字段值
                String checkStatus = "authenticity".equals(verifyType) ? "真实性验证" : "有效性验证";
                checkType.setCheckStatus(checkStatus);  // 验证类型
                checkType.setDataStatus("驾驶证");      // 数据类型：驾驶证
                checkType.setCheckData(result.getLicenseNumber());  // 验证的数据（驾驶证号）
                checkType.setCheckName(result.getName());           // 名称
                
                // hd_result: 将verifyResult转换为对应的结果
                String hdResult = convertVerifyResult(result.getVerifyResult());
                checkType.setHdResult(hdResult);
                
                // detail: 只有不通过的情况下才存放message
                if ("不通过".equals(hdResult)) {
                    checkType.setDetail(result.getMessage());
                } else {
                    checkType.setDetail(null);  // 通过和不存在的情况不存放detail
                }
                
                // 设置创建时间
                checkType.setCreateTime(DateUtils.getNowDate());
                
                // 保存到数据库
                checkTypeService.insertCheckType(checkType);
                savedCount++;
                
            } catch (Exception e) {
                logger.error("保存驾驶证验证结果失败: {}", result, e);
            }
        }
        
        return savedCount;
    }
    
    private int processQualificationVerification(List<CheckDriver> drivers, String verifyType) {
        // 调用从业资格证验证接口
        QualificationVerifyResponse response = callQualificationVerifyApi(drivers, verifyType);

        if (response != null) {
            List<QualificationVerifyResponse.QualificationResult> results = null;

            // 判断是否为加密接口返回的数据结构（直接包含results字段）
            if (response.getResults() != null) {
                // 加密接口返回的数据结构
                results = response.getResults();
                logger.info("处理加密接口返回的从业资格证验证结果，共 {} 条", results.size());
            } else if (response.getCode() != null && response.getCode() == 200 && response.getData() != null) {
                // 非加密接口返回的数据结构
                results = response.getData().getResults();
                logger.info("处理非加密接口返回的从业资格证验证结果，共 {} 条", results != null ? results.size() : 0);
            }

            if (results != null && !results.isEmpty()) {
                // 保存验证结果
                int saved = saveQualificationVerifyResults(results, verifyType);
                logger.info("成功保存 {} 条从业资格证{}验证结果", saved,
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return saved;
            } else {
                logger.warn("从业资格证{}验证接口返回空结果",
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return 0;
            }
        } else {
            logger.error("从业资格证{}验证接口调用失败，响应为null",
                "authenticity".equals(verifyType) ? "真实性" : "有效性");
            return 0;
        }
    }
    
    private QualificationVerifyResponse callQualificationVerifyApi(List<CheckDriver> drivers, String verifyType) {
        try {
            // 构建请求数据
            QualificationVerifyRequest request = new QualificationVerifyRequest();
            request.setVerifyType(verifyType); // 设置验证类型（authenticity或validity）
            List<QualificationVerifyRequest.QualificationData> qualifications = new ArrayList<>();
            
            for (CheckDriver driver : drivers) {
                // 只有拥有从业资格证信息的司机才进行验证
                if (StringUtils.isNotEmpty(driver.getCERTIFICATE())) {
                    // 处理日期格式
                    String validStartDate = driver.getCertificateFrom() != null ? 
                        new java.text.SimpleDateFormat("yyyy-MM-dd").format(driver.getCertificateFrom()) : null;
                    String validEndDate = driver.getCertificateTo() != null ? 
                        new java.text.SimpleDateFormat("yyyy-MM-dd").format(driver.getCertificateTo()) : null;
                    
                    QualificationVerifyRequest.QualificationData qualificationData = new QualificationVerifyRequest.QualificationData(
                        driver.getNAME(),
                        driver.getCERTIFICATE(),
                        validStartDate,
                        validEndDate
                    );
                    qualifications.add(qualificationData);
                }
            }
            
            if (qualifications.isEmpty()) {
                logger.warn("当前批次没有有效的从业资格证数据");
                return null;
            }
            
            request.setQualifications(qualifications);
            
            // 根据配置选择加密或非加密调用
            if (useEncryption) {
                logger.info("使用加密传输调用从业资格证验证接口");
                try {
                    return callEncryptedQualificationVerifyApi(request);
                } catch (Exception e) {
                    logger.error("调用加密从业资格证验证接口失败", e);
                    return null;  // 返回null而不是抛出异常
                }
            } else {
                logger.info("使用明文传输调用从业资格证验证接口");
                // 设置HTTP头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                
                HttpEntity<QualificationVerifyRequest> entity = new HttpEntity<>(request, headers);
                
                // 调用接口
                ResponseEntity<QualificationVerifyResponse> response = restTemplate.postForEntity(
                    qualificationVerifyUrl, entity, QualificationVerifyResponse.class);
                
                return response.getBody();
            }
            
        } catch (Exception e) {
            logger.error("调用从业资格证验证接口失败", e);
            return null;
        }
    }
    
    private int saveQualificationVerifyResults(List<QualificationVerifyResponse.QualificationResult> results, String verifyType) {
        if (results == null || results.isEmpty()) {
            return 0;
        }
        
        int savedCount = 0;
        
        for (QualificationVerifyResponse.QualificationResult result : results) {
            try {
                CheckType checkType = new CheckType();
                
                // 根据验证类型设置字段值
                String checkStatus = "authenticity".equals(verifyType) ? "真实性验证" : "有效性验证";
                checkType.setCheckStatus(checkStatus);  // 验证类型
                checkType.setDataStatus("从业资格证");    // 数据类型：从业资格证
                checkType.setCheckData(result.getQualificationNumber());  // 验证的数据（从业资格证号）
                checkType.setCheckName(result.getName());                 // 名称
                
                // hd_result: 将verifyResult转换为对应的结果
                String hdResult = convertVerifyResult(result.getVerifyResult());
                checkType.setHdResult(hdResult);
                
                // detail: 只有不通过的情况下才存放message
                if ("不通过".equals(hdResult)) {
                    checkType.setDetail(result.getMessage());
                } else {
                    checkType.setDetail(null);  // 通过和不存在的情况不存放detail
                }
                
                // 设置创建时间
                checkType.setCreateTime(DateUtils.getNowDate());
                
                // 保存到数据库
                checkTypeService.insertCheckType(checkType);
                savedCount++;
                
            } catch (Exception e) {
                logger.error("保存从业资格证验证结果失败: {}", result, e);
            }
        }
        
        return savedCount;
    }
    
    /**
     * 将验证结果转换为对应的文字描述
     * @param verifyResult 验证结果
     * @return 对应的结果描述
     */
    private String convertVerifyResult(String verifyResult) {
        if (StringUtils.isEmpty(verifyResult)) {
            return "不存在";  // 默认为不存在
        }
        
        switch (verifyResult) {
            case "一致":
            case "有效":
                return "通过";  // 一致、有效都是通过
            case "不一致":
                return "不通过";  // 不一致都是不通过
            case "不存在":
                return "不存在";  // 不存在
            default:
                return "不存在";  // 默认为不存在
        }
    }
    
    @Override
    public String getBatchTaskStatus() {
        if (isRunning.get()) {
            return String.format("任务运行中 - %s", lastMessage);
        } else {
            return String.format("任务已停止 - %s", lastMessage);
        }
    }
    
    /**
     * 调用加密手机号验证接口
     */
    private PhoneVerifyResponse callEncryptedPhoneVerifyApi(PhoneVerifyRequest request) {
        try {
            // 1. 序列化请求数据
            String requestJson = objectMapper.writeValueAsString(request);
            
            // 2. 加密请求数据
            String encryptedData = AESUtils.encrypt(requestJson);
            
            // 3. 构建加密请求包装
            Map<String, Object> encryptedRequest = new HashMap<>();
            encryptedRequest.put("encryptedData", encryptedData);
            encryptedRequest.put("timestamp", System.currentTimeMillis());
            
            // 4. 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);
            
            // 5. 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(encryptedPhoneVerifyUrl, entity, Map.class);
            
            logger.info("加密接口响应状态: {}", response.getStatusCode());
            logger.info("加密接口响应体: {}", response.getBody());
            
            if (response.getBody() != null && response.getBody().get("code") != null && response.getBody().get("code").equals(200)) {
                // 6. 提取加密响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) response.getBody().get("data");
                
                if (responseData == null) {
                    logger.error("响应中的data字段为null");
                    return null;
                }
                
                String encryptedResponseData = (String) responseData.get("encryptedData");
                
                if (encryptedResponseData == null) {
                    logger.error("响应中的encryptedData字段为null，responseData内容: {}", responseData);
                    return null;
                }
                
                // 7. 解密响应数据
                String decryptedResponse = AESUtils.decrypt(encryptedResponseData);
                logger.info("解密后的响应数据: {}", decryptedResponse);
                
                // 8. 反序列化为目标对象
                PhoneVerifyResponse phoneResponse = objectMapper.readValue(decryptedResponse, PhoneVerifyResponse.class);
                logger.info("反序列化后的响应对象: code={}, verifyType={}, total={}, results={}",
                    phoneResponse.getCode(), phoneResponse.getVerifyType(), phoneResponse.getTotal(),
                    phoneResponse.getResults() != null ? phoneResponse.getResults().size() : "null");
                return phoneResponse;
            } else {
                logger.error("加密API调用失败，响应: {}", response.getBody());
                return null;
            }
            
        } catch (Exception e) {
            logger.error("调用加密手机号验证接口失败", e);
            return null;
        }
    }

    /**
     * 调用加密身份证验证接口
     */
    private IdCardVerifyResponse callEncryptedIdCardVerifyApi(IdCardVerifyRequest request) {
        try {
            // 1. 序列化请求数据
            String requestJson = objectMapper.writeValueAsString(request);
            
            // 2. 加密请求数据
            String encryptedData = AESUtils.encrypt(requestJson);
            
            // 3. 构建加密请求包装
            Map<String, Object> encryptedRequest = new HashMap<>();
            encryptedRequest.put("encryptedData", encryptedData);
            encryptedRequest.put("timestamp", System.currentTimeMillis());
            
            // 4. 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);
            
            // 5. 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(encryptedIdCardVerifyUrl, entity, Map.class);
            
            logger.info("身份证加密接口响应状态: {}", response.getStatusCode());
            logger.info("身份证加密接口响应体: {}", response.getBody());
            
            if (response.getBody() != null && response.getBody().get("code") != null && response.getBody().get("code").equals(200)) {
                // 6. 提取加密响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) response.getBody().get("data");
                
                if (responseData == null) {
                    logger.error("身份证验证响应中的data字段为null");
                    throw new RuntimeException("身份证验证响应中的data字段为null");
                }
                
                String encryptedResponseData = (String) responseData.get("encryptedData");
                
                if (encryptedResponseData == null) {
                    logger.error("身份证验证响应中的encryptedData字段为null，responseData内容: {}", responseData);
                    throw new RuntimeException("身份证验证响应中的encryptedData字段为null");
                }
                
                // 7. 解密响应数据
                String decryptedResponse = AESUtils.decrypt(encryptedResponseData);
                
                // 8. 反序列化为目标对象
                return objectMapper.readValue(decryptedResponse, IdCardVerifyResponse.class);
            } else {
                logger.error("身份证加密API调用失败，响应: {}", response.getBody());
                throw new RuntimeException("身份证加密API调用失败：" + response.getBody());
            }
            
        } catch (Exception e) {
            logger.error("调用加密身份证验证接口失败", e);
            throw new RuntimeException("调用加密身份证验证接口失败", e);
        }
    }

    /**
     * 调用加密驾驶证验证接口
     */
    private DrivingLicenseVerifyResponse callEncryptedLicenseVerifyApi(DrivingLicenseVerifyRequest request) {
        try {
            // 1. 序列化请求数据
            String requestJson = objectMapper.writeValueAsString(request);
            
            // 2. 加密请求数据
            String encryptedData = AESUtils.encrypt(requestJson);
            
            // 3. 构建加密请求包装
            Map<String, Object> encryptedRequest = new HashMap<>();
            encryptedRequest.put("encryptedData", encryptedData);
            encryptedRequest.put("timestamp", System.currentTimeMillis());
            
            // 4. 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);
            
            // 5. 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(encryptedLicenseVerifyUrl, entity, Map.class);
            
            logger.info("驾驶证加密接口响应状态: {}", response.getStatusCode());
            logger.info("驾驶证加密接口响应体: {}", response.getBody());
            
            if (response.getBody() != null && response.getBody().get("code") != null && response.getBody().get("code").equals(200)) {
                // 6. 提取加密响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) response.getBody().get("data");
                
                if (responseData == null) {
                    logger.error("驾驶证验证响应中的data字段为null");
                    return null;
                }

                String encryptedResponseData = (String) responseData.get("encryptedData");

                if (encryptedResponseData == null) {
                    logger.error("驾驶证验证响应中的encryptedData字段为null，responseData内容: {}", responseData);
                    return null;
                }

                // 7. 解密响应数据
                String decryptedResponse = AESUtils.decrypt(encryptedResponseData);

                // 8. 反序列化为目标对象
                return objectMapper.readValue(decryptedResponse, DrivingLicenseVerifyResponse.class);
            } else {
                logger.error("驾驶证加密API调用失败，响应: {}", response.getBody());
                return null;
            }

        } catch (Exception e) {
            logger.error("调用加密驾驶证验证接口失败", e);
            return null;
        }
    }

    /**
     * 调用加密从业资格证验证接口
     */
    private QualificationVerifyResponse callEncryptedQualificationVerifyApi(QualificationVerifyRequest request) {
        try {
            // 1. 序列化请求数据
            String requestJson = objectMapper.writeValueAsString(request);
            
            // 2. 加密请求数据
            String encryptedData = AESUtils.encrypt(requestJson);
            
            // 3. 构建加密请求包装
            Map<String, Object> encryptedRequest = new HashMap<>();
            encryptedRequest.put("encryptedData", encryptedData);
            encryptedRequest.put("timestamp", System.currentTimeMillis());
            
            // 4. 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);
            
            // 5. 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(encryptedQualificationVerifyUrl, entity, Map.class);
            
            logger.info("从业资格证加密接口响应状态: {}", response.getStatusCode());
            logger.info("从业资格证加密接口响应体: {}", response.getBody());
            
            if (response.getBody() != null && response.getBody().get("code") != null && response.getBody().get("code").equals(200)) {
                // 6. 提取加密响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) response.getBody().get("data");
                
                if (responseData == null) {
                    logger.error("从业资格证验证响应中的data字段为null");
                    return null;
                }

                String encryptedResponseData = (String) responseData.get("encryptedData");

                if (encryptedResponseData == null) {
                    logger.error("从业资格证验证响应中的encryptedData字段为null，responseData内容: {}", responseData);
                    return null;
                }

                // 7. 解密响应数据
                String decryptedResponse = AESUtils.decrypt(encryptedResponseData);

                // 8. 反序列化为目标对象
                return objectMapper.readValue(decryptedResponse, QualificationVerifyResponse.class);
            } else {
                logger.error("从业资格证加密API调用失败，响应: {}", response.getBody());
                return null;
            }

        } catch (Exception e) {
            logger.error("调用加密从业资格证验证接口失败", e);
            return null;
        }
    }
}
