//package com.ruoyi.check.controller;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.ruoyi.check.utils.AESUtils;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 简化的AES加密测试控制器
// *
// * <AUTHOR>
// * @date 2025-01-20
// */
//@Api(tags = "AES加密测试")
//@RestController
//@RequestMapping("/check/aes/test")
//public class SimpleEncryptionTestController extends BaseController {
//
//    private static final Logger logger = LoggerFactory.getLogger(SimpleEncryptionTestController.class);
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    private final ObjectMapper objectMapper = new ObjectMapper();
//
//    @Value("${driver.verify.use.encryption:false}")
//    private boolean useEncryption;
//
//    @Value("${driver.verify.encrypted.idcard.url:http://localhost:8080/api/v1/huoda/encrypted/driver/verify/idcard}")
//    private String encryptedIdCardVerifyUrl;
//
//    @Value("${driver.verify.idcard.url:http://localhost:8080/api/v1/huoda/driver/verify/idcard}")
//    private String idCardVerifyUrl;
//
//    /**
//     * AES加密解密测试
//     */
//    @ApiOperation(value = "AES加密解密测试", notes = "测试AES加密和解密功能")
//    @PostMapping("/aes")
//    public AjaxResult testAESEncryption(@RequestBody Map<String, Object> testData) {
//        try {
//            logger.info("开始AES加密测试");
//
//            // 1. 序列化测试数据
//            String originalData = objectMapper.writeValueAsString(testData);
//            logger.info("原始数据: {}", originalData);
//
//            // 2. AES加密
//            String encryptedData = AESUtils.encrypt(originalData);
//            logger.info("加密后数据: {}", encryptedData);
//
//            // 3. AES解密
//            String decryptedData = AESUtils.decrypt(encryptedData);
//            logger.info("解密后数据: {}", decryptedData);
//
//            // 4. 验证数据一致性
//            boolean isConsistent = originalData.equals(decryptedData);
//
//            Map<String, Object> result = new HashMap<>();
//            result.put("originalData", originalData);
//            result.put("encryptedData", encryptedData);
//            result.put("decryptedData", decryptedData);
//            result.put("isConsistent", isConsistent);
//            result.put("useEncryption", useEncryption);
//
//            return AjaxResult.success("AES加密测试完成", result);
//
//        } catch (Exception e) {
//            logger.error("AES加密测试失败", e);
//            return AjaxResult.error("AES加密测试失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 测试调用加密接口
//     */
//    @ApiOperation(value = "测试调用加密接口", notes = "测试调用ruoyi-mock的加密接口")
//    @PostMapping("/call-encrypted-api")
//    public AjaxResult testCallEncryptedApi(@RequestBody Map<String, Object> testData) {
//        try {
//            logger.info("开始测试调用加密接口");
//
//            // 1. 序列化请求数据
//            String requestJson = objectMapper.writeValueAsString(testData);
//            logger.info("请求数据: {}", requestJson);
//
//            // 2. 加密请求数据
//            String encryptedData = AESUtils.encrypt(requestJson);
//
//            // 3. 构建加密请求包装
//            Map<String, Object> encryptedRequest = new HashMap<>();
//            encryptedRequest.put("encryptedData", encryptedData);
//            encryptedRequest.put("timestamp", System.currentTimeMillis());
//
//            // 4. 设置HTTP头
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//
//            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);
//
//            // 5. 调用加密接口（这里使用身份证验证接口作为示例）
//            String encryptedApiUrl = "http://localhost:8973/api/v1/huoda/encrypted/driver/verify/idcard";
//            ResponseEntity<Map> response = restTemplate.postForEntity(encryptedApiUrl, entity, Map.class);
//
//            logger.info("接口响应: {}", response.getBody());
//
//            Map<String, Object> result = new HashMap<>();
//            result.put("requestData", testData);
//            result.put("encryptedRequest", encryptedRequest);
//            result.put("response", response.getBody());
//
//            return AjaxResult.success("加密接口调用测试完成", result);
//
//        } catch (Exception e) {
//            logger.error("加密接口调用测试失败", e);
//            return AjaxResult.error("加密接口调用测试失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 显示当前加密配置状态
//     */
//    @ApiOperation(value = "显示加密配置状态", notes = "显示当前的加密配置状态")
//    @GetMapping("/config")
//    public AjaxResult showConfig() {
//        try {
//            Map<String, Object> config = new HashMap<>();
//            config.put("useEncryption", useEncryption);
//            config.put("encryptedIdCardVerifyUrl", encryptedIdCardVerifyUrl);
//            config.put("idCardVerifyUrl", idCardVerifyUrl);
//
//            logger.info("当前加密配置: useEncryption={}, encryptedIdCardVerifyUrl={}, idCardVerifyUrl={}",
//                useEncryption, encryptedIdCardVerifyUrl, idCardVerifyUrl);
//
//            return AjaxResult.success("获取配置成功", config);
//
//        } catch (Exception e) {
//            logger.error("获取配置失败", e);
//            return AjaxResult.error("获取配置失败: " + e.getMessage());
//        }
//    }
//}