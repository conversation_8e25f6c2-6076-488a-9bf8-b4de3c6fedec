package com.ruoyi.check.service;

import com.ruoyi.check.domain.*;
import com.ruoyi.check.utils.VerifyResultHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 司机验证服务
 */
@Service
public class DriverVerifyService {
    
    private final Random random = new Random();
    
    /**
     * 手机号码批量验证
     */
    public VerifyResponseData verifyPhone(DriverPhoneVerifyRequest request) {
        VerifyResponseData responseData = new VerifyResponseData();
        responseData.setTotal(request.getPhones().size());
        
        List<VerifyResponseData.VerifyResult> results = new ArrayList<>();
        for (DriverPhoneVerifyRequest.DriverPhoneData data : request.getPhones()) {
            VerifyResponseData.VerifyResult result = new VerifyResponseData.VerifyResult();
            result.setPhone(mockDecryptPhone(data.getPhone()));
            result.setName(data.getName());
            // 设置身份证号（如果有的话）
            if (data.getIdCard() != null) {
                result.setIdCard(mockDecryptIdCard(data.getIdCard()));
            }
            
            // 模拟验证结果
            String[] verifyResults = {"一致", "不一致", "失效", "不存在"};
            String verifyResult = verifyResults[random.nextInt(verifyResults.length)];
            VerifyResultHelper.setResultCode(result, verifyResult);
            
            switch (verifyResult) {
                case "一致":
                    result.setMessage("验证成功");
                    break;
                case "不一致":
                    result.setMessage("手机号与开卡人信息不匹配");
                    break;
                case "失效":
                    result.setMessage("手机号已失效");
                    break;
                case "不存在":
                    result.setMessage("手机号码不存在");
                    break;
            }
            
            results.add(result);
        }
        
        responseData.setResults(results);
        return responseData;
    }
    
    /**
     * 身份证批量验证
     */
    public VerifyResponseData verifyIdCard(DriverIdCardVerifyRequest request) {
        VerifyResponseData responseData = new VerifyResponseData();
        responseData.setVerifyType(request.getVerifyType());
        responseData.setTotal(request.getIdCards().size());
        
        List<VerifyResponseData.VerifyResult> results = new ArrayList<>();
        for (DriverIdCardVerifyRequest.DriverIdCardData data : request.getIdCards()) {
            VerifyResponseData.VerifyResult result = new VerifyResponseData.VerifyResult();
            result.setIdCard(mockDecryptIdCard(data.getIdCard()));
            result.setName(data.getName());
            
            // 模拟验证结果
            String[] verifyResults = {"一致", "不一致", "失效", "不存在"};
            String verifyResult = verifyResults[random.nextInt(verifyResults.length)];
            VerifyResultHelper.setResultCode(result, verifyResult);
            
            switch (verifyResult) {
                case "一致":
                    result.setMessage("验证成功");
                    break;
                case "不一致":
                    result.setMessage("身份证与姓名不匹配");
                    break;
                case "失效":
                    result.setMessage("身份证已失效");
                    break;
                case "不存在":
                    result.setMessage("身份证不存在");
                    break;
            }
            
            results.add(result);
        }
        
        responseData.setResults(results);
        return responseData;
    }
    
    /**
     * 驾驶证批量验证
     */
    public VerifyResponseData verifyDriverLicense(DriverLicenseVerifyRequest request) {
        VerifyResponseData responseData = new VerifyResponseData();
        responseData.setVerifyType(request.getVerifyType());
        responseData.setTotal(request.getLicenses().size());
        
        List<VerifyResponseData.VerifyResult> results = new ArrayList<>();
        for (DriverLicenseVerifyRequest.DriverLicenseData data : request.getLicenses()) {
            VerifyResponseData.VerifyResult result = new VerifyResponseData.VerifyResult();
            result.setIdCard(mockDecryptIdCard(data.getIdCard()));
            result.setName(data.getName());
            result.setLicenseNumber(data.getLicenseNumber());
            
            // 模拟验证结果
            String[] verifyResults = {"一致", "不一致", "失效", "不存在"};
            String verifyResult = verifyResults[random.nextInt(verifyResults.length)];
            VerifyResultHelper.setResultCode(result, verifyResult);
            
            switch (verifyResult) {
                case "一致":
                    result.setMessage("验证成功");
                    break;
                case "不一致":
                    result.setMessage("驾驶证信息与身份证不匹配");
                    break;
                case "失效":
                    result.setMessage("驾驶证已失效");
                    break;
                case "不存在":
                    result.setMessage("驾驶证不存在");
                    break;
            }
            
            results.add(result);
        }
        
        responseData.setResults(results);
        return responseData;
    }
    
    /**
     * 从业资格证批量验证
     */
    public VerifyResponseData verifyQualification(DriverQualificationVerifyRequest request) {
        VerifyResponseData responseData = new VerifyResponseData();
        responseData.setVerifyType(request.getVerifyType());
        responseData.setTotal(request.getQualifications().size());
        
        List<VerifyResponseData.VerifyResult> results = new ArrayList<>();
        for (DriverQualificationVerifyRequest.DriverQualificationData data : request.getQualifications()) {
            VerifyResponseData.VerifyResult result = new VerifyResponseData.VerifyResult();
            result.setQualificationNumber(data.getQualificationNumber());
            result.setName(data.getName());
            
            // 模拟验证结果
            String[] verifyResults = {"一致", "不一致", "失效", "不存在"};
            String verifyResult = verifyResults[random.nextInt(verifyResults.length)];
            VerifyResultHelper.setResultCode(result, verifyResult);
            
            switch (verifyResult) {
                case "一致":
                    result.setMessage("验证成功");
                    break;
                case "不一致":
                    result.setMessage("从业资格证信息不匹配");
                    break;
                case "失效":
                    result.setMessage("从业资格证已失效");
                    break;
                case "不存在":
                    result.setMessage("从业资格证不存在");
                    break;
            }
            
            results.add(result);
        }
        
        responseData.setResults(results);
        return responseData;
    }
    
    /**
     * 模拟解密手机号
     */
    private String mockDecryptPhone(String encryptedPhone) {
        // 这里只是模拟，实际应该进行解密
        return "131" + String.format("%08d", random.nextInt(100000000));
    }
    
    /**
     * 模拟解密身份证号
     */
    private String mockDecryptIdCard(String encryptedIdCard) {
        // 这里只是模拟，实际应该进行解密
        return "610528199" + String.format("%09d", random.nextInt(1000000000));
    }
} 