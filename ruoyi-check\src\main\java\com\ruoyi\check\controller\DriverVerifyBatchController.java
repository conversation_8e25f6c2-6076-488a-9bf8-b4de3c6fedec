package com.ruoyi.check.controller;

import com.ruoyi.check.service.IDriverVerifyBatchService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 司机验证批处理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/check/driver/batch")
public class DriverVerifyBatchController extends BaseController {
    
    @Autowired
    private IDriverVerifyBatchService driverVerifyBatchService;
    
    /**
     * 启动批量验证司机手机号任务
     */
    @Anonymous
    @Log(title = "司机手机号验证", businessType = BusinessType.OTHER)
    @PostMapping("/verifyPhones")
    public AjaxResult startBatchVerifyPhones() {
        String result = driverVerifyBatchService.batchVerifyDriverPhones();
        return AjaxResult.success(result);
    }
    
    /**
     * 启动批量验证司机身份证任务（真实性验证）
     */
    @Anonymous
    @Log(title = "司机身份证真实性验证", businessType = BusinessType.OTHER)
    @PostMapping("/verifyIdCards")
    public AjaxResult startBatchVerifyIdCards() {
        String result = driverVerifyBatchService.batchVerifyDriverIdCards();
        return AjaxResult.success(result);
    }
    
    /**
     * 启动批量验证司机身份证任务（有效性验证）
     */
    @Anonymous
    @Log(title = "司机身份证有效性验证", businessType = BusinessType.OTHER)
    @PostMapping("/verifyIdCardValidity")
    public AjaxResult startBatchVerifyIdCardValidity() {
        String result = driverVerifyBatchService.batchVerifyDriverIdCardValidity();
        return AjaxResult.success(result);
    }
    
    /**
     * 启动批量验证司机驾驶证任务（真实性验证）
     */
    @Anonymous
    @Log(title = "司机驾驶证真实性验证", businessType = BusinessType.OTHER)
    @PostMapping("/verifyLicense")
    public AjaxResult startBatchVerifyLicense() {
        String result = driverVerifyBatchService.batchVerifyDriverLicense();
        return AjaxResult.success(result);
    }
    
    /**
     * 启动批量验证司机驾驶证任务（有效性验证）
     */
    @Anonymous
    @Log(title = "司机驾驶证有效性验证", businessType = BusinessType.OTHER)
    @PostMapping("/verifyLicenseValidity")
    public AjaxResult startBatchVerifyLicenseValidity() {
        String result = driverVerifyBatchService.batchVerifyDriverLicenseValidity();
        return AjaxResult.success(result);
    }
    
    /**
     * 启动批量验证司机从业资格证任务（真实性验证）
     */
    @Anonymous
    @Log(title = "司机从业资格证真实性验证", businessType = BusinessType.OTHER)
    @PostMapping("/verifyQualification")
    public AjaxResult startBatchVerifyQualification() {
        String result = driverVerifyBatchService.batchVerifyDriverQualification();
        return AjaxResult.success(result);
    }
    
    /**
     * 启动批量验证司机从业资格证任务（有效性验证）
     */
    @Anonymous
    @Log(title = "司机从业资格证有效性验证", businessType = BusinessType.OTHER)
    @PostMapping("/verifyQualificationValidity")
    public AjaxResult startBatchVerifyQualificationValidity() {
        String result = driverVerifyBatchService.batchVerifyDriverQualificationValidity();
        return AjaxResult.success(result);
    }
    
    /**
     * 启动批量验证司机所有信息任务（手机号+身份证+驾驶证+从业资格证）
     */
    @Anonymous
    @Log(title = "司机全量验证", businessType = BusinessType.OTHER)
    @PostMapping("/verifyAll")
    public AjaxResult startBatchVerifyAll() {
        String result = driverVerifyBatchService.batchVerifyDriverAll();
        return AjaxResult.success(result);
    }
    
    /**
     * 获取批处理任务状态
     */
    @GetMapping("/status")
    public AjaxResult getBatchTaskStatus() {
        String status = driverVerifyBatchService.getBatchTaskStatus();
        return AjaxResult.success(status);
    }
} 