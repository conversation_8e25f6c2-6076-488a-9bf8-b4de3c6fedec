package com.ruoyi.check.controller;

import com.ruoyi.check.service.IVehicleTrackService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 车辆轨迹查询控制器
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/check/vehicle/track")
public class CheckVehicleTrackController extends BaseController {
    
    @Autowired
    private IVehicleTrackService vehicleTrackService;
    
    /**
     * 查询车辆历史轨迹
     * 
     * @param plateNumber 车牌号
     * @param loadTime 装车时间
     * @param arriveTime 运抵时间
     * @return 轨迹查询结果
     */
    @Anonymous
    @Log(title = "车辆轨迹查询", businessType = BusinessType.OTHER)
    @PostMapping("/history")
    public AjaxResult queryTrackHistory(@RequestBody VehicleTrackQueryRequest request) {
        return vehicleTrackService.queryVehicleTrackHistoryBatch(request.getVehicles());
    }
} 