package com.ruoyi.check.service.impl;

import com.ruoyi.check.domain.CheckVehicle;
import com.ruoyi.check.domain.CheckType;
import com.ruoyi.check.service.ICheckVehicleService;
import com.ruoyi.check.service.ICheckTypeService;
import com.ruoyi.check.service.IVehicleVerifyBatchService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 车辆验证批处理服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class VehicleVerifyBatchServiceImpl implements IVehicleVerifyBatchService {
    
    private static final Logger logger = LoggerFactory.getLogger(VehicleVerifyBatchServiceImpl.class);
    
    @Autowired
    private ICheckVehicleService checkVehicleService;
    
    @Autowired
    private ICheckTypeService checkTypeService;
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 每批处理的数量
    private static final int BATCH_SIZE = 200;
    
    // 批次间隔时间（毫秒）
    private static final long BATCH_INTERVAL = 2000;
    
    // 车辆行驶证验证接口URL
    @Value("${vehicle.verify.encrypted.drivinglicense.url:http://localhost:8080/api/v1/huoda/vehicle/verify/drivinglicense}")
    private String vehicleDrivingLicenseVerifyUrl;
    
    // 车辆道路运输证验证接口URL
    @Value("${vehicle.verify.encrypted.roadtransport.url:http://localhost:8080/api/v1/huoda/vehicle/verify/roadtransport}")
    private String vehicleRoadTransportVerifyUrl;
    
    // 任务状态
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong totalCount = new AtomicLong(0);
    private String lastMessage = "未开始执行";
    
    @Override
    public String batchVerifyVehicleDrivingLicense() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("authenticity")).start();
        
        return "车辆行驶证真实性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyVehicleDrivingLicenseValidity() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("validity")).start();
        
        return "车辆行驶证有效性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyVehicleRoadTransport() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeRoadTransportTask("authenticity")).start();
        
        return "车辆道路运输证真实性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyVehicleRoadTransportValidity() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeRoadTransportTask("validity")).start();
        
        return "车辆道路运输证有效性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyVehicleAll() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeVehicleAllTask()).start();
        
        return "车辆所有证件验证批处理任务已启动，正在后台执行";
    }
    
    private void executeTask(String verifyType) {
        try {
            isRunning.set(true);
            processedCount.set(0);
            
            // 获取总数据量
            long total = checkVehicleService.countCheckVehicles();
            totalCount.set(total);
            
            logger.info("开始批量验证车辆行驶证信息，验证类型: {}, 总数据量: {}", verifyType, total);
            lastMessage = String.format("开始批量验证，验证类型: %s, 总数据量: %d", verifyType, total);
            
            long offset = 0;
            int successCount = 0;
            int errorCount = 0;
            
            while (offset < total) {
                try {
                    // 分批查询车辆数据
                    List<CheckVehicle> vehicles = checkVehicleService.selectCheckVehicleListByPage(offset, BATCH_SIZE);
                    
                    if (vehicles.isEmpty()) {
                        break;
                    }
                    
                    logger.info("处理第 {} 批数据，数量: {}", (offset / BATCH_SIZE + 1), vehicles.size());
                    
                    // 处理车辆行驶证验证
                    successCount += processVehicleDrivingLicenseVerification(vehicles, verifyType);
                    
                    processedCount.addAndGet(vehicles.size());
                    offset += BATCH_SIZE;
                    
                    // 更新状态消息
                    lastMessage = String.format("已处理: %d/%d, 成功: %d, 失败: %d", 
                        processedCount.get(), total, successCount, errorCount);
                    
                    // 批次间隔
                    if (offset < total) {
                        Thread.sleep(BATCH_INTERVAL);
                    }
                    
                } catch (Exception e) {
                    logger.error("处理批次数据时发生错误", e);
                    errorCount += BATCH_SIZE;
                    offset += BATCH_SIZE;
                }
            }
            
            lastMessage = String.format("批处理完成！总处理: %d, 成功: %d, 失败: %d", 
                total, successCount, errorCount);
            logger.info(lastMessage);
            
        } catch (Exception e) {
            logger.error("批处理任务执行失败", e);
            lastMessage = "批处理任务执行失败: " + e.getMessage();
        } finally {
            isRunning.set(false);
        }
    }
    
    private int processVehicleDrivingLicenseVerification(List<CheckVehicle> vehicles, String verifyType) {
        // 调用车辆行驶证验证接口
        AjaxResult response = callVehicleDrivingLicenseVerifyApi(vehicles, verifyType);
        
        if (response != null && response.get("code").equals(200) && response.get("data") != null) {
            // 保存验证结果
            Map<String, Object> data = (Map<String, Object>) response.get("data");
            List<Map<String, Object>> results = (List<Map<String, Object>>) data.get("results");
            int saved = saveVehicleDrivingLicenseVerifyResults(results, verifyType);
            logger.info("成功保存 {} 条车辆行驶证{}验证结果", saved, 
                "authenticity".equals(verifyType) ? "真实性" : "有效性");
            return saved;
        } else {
            logger.error("车辆行驶证{}验证接口调用失败，响应: {}", 
                "authenticity".equals(verifyType) ? "真实性" : "有效性", response);
            return 0;
        }
    }
    
    private AjaxResult callVehicleDrivingLicenseVerifyApi(List<CheckVehicle> vehicles, String verifyType) {
        try {
            // 构建请求数据
            Map<String, Object> request = new HashMap<>();
            request.put("verifyType", verifyType); // 设置验证类型（authenticity或validity）
            List<Map<String, Object>> vehicleList = new ArrayList<>();
            
            for (CheckVehicle vehicle : vehicles) {
                // 只有拥有车牌号和车辆识别代码的车辆才进行验证
                if (StringUtils.isNotEmpty(vehicle.getPlateNo()) && 
                    StringUtils.isNotEmpty(vehicle.getCODE()) &&
                    StringUtils.isNotEmpty(vehicle.getOWNER())) {
                    
                    // 处理日期格式
                    String validEndDate = vehicle.getIssueExpiryDate() != null ? 
                        new java.text.SimpleDateFormat("yyyy-MM-dd").format(vehicle.getIssueExpiryDate()) : null;
                    
                    Map<String, Object> vehicleData = new HashMap<>();
                    vehicleData.put("plateNumber", vehicle.getPlateNo());
                    vehicleData.put("vehicleIdentificationCode", vehicle.getCODE());
                    vehicleData.put("vehicleType", vehicle.getVclTpNm());
                    vehicleData.put("owner", vehicle.getOWNER());
                    vehicleData.put("validEndDate", validEndDate);
                    
                    vehicleList.add(vehicleData);
                }
            }
            
            if (vehicleList.isEmpty()) {
                logger.warn("当前批次没有有效的车辆行驶证数据");
                return null;
            }
            
            request.put("vehicles", vehicleList);
            
            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            // 调用接口
            ResponseEntity<AjaxResult> response = restTemplate.postForEntity(
                vehicleDrivingLicenseVerifyUrl, entity, AjaxResult.class);
            
            return response.getBody();
            
        } catch (Exception e) {
            logger.error("调用车辆行驶证验证接口失败", e);
            return null;
        }
    }
    
    private int saveVehicleDrivingLicenseVerifyResults(List<Map<String, Object>> results, String verifyType) {
        if (results == null || results.isEmpty()) {
            return 0;
        }
        
        int savedCount = 0;
        
        for (Map<String, Object> result : results) {
            try {
                CheckType checkType = new CheckType();
                
                // 根据验证类型设置字段值
                String checkStatus = "authenticity".equals(verifyType) ? "真实性验证" : "有效性验证";
                checkType.setCheckStatus(checkStatus);  // 验证类型
                checkType.setDataStatus("行驶证");      // 数据类型：行驶证
                checkType.setCheckData(String.valueOf(result.get("plateNumber")));  // 验证的数据（车牌号）
                checkType.setCheckName(String.valueOf(result.get("owner")));        // 车主姓名
                
                // hd_result: 将verifyResult转换为对应的结果
                String hdResult = convertVerifyResult(String.valueOf(result.get("verifyResult")));
                checkType.setHdResult(hdResult);
                
                // detail: 只有不通过的情况下才存放message
                if ("不通过".equals(hdResult)) {
                    checkType.setDetail(String.valueOf(result.get("message")));
                } else {
                    checkType.setDetail(null);  // 通过和不存在的情况不存放detail
                }
                
                // 设置创建时间
                checkType.setCreateTime(DateUtils.getNowDate());
                
                // 保存到数据库
                checkTypeService.insertCheckType(checkType);
                savedCount++;
                
            } catch (Exception e) {
                logger.error("保存车辆行驶证验证结果失败: {}", result, e);
            }
        }
        
        return savedCount;
    }
    
    /**
     * 将验证结果转换为对应的文字描述
     * @param verifyResult 验证结果
     * @return 对应的结果描述
     */
    private String convertVerifyResult(String verifyResult) {
        if (StringUtils.isEmpty(verifyResult)) {
            return "不存在";  // 默认为不存在
        }
        
        switch (verifyResult) {
            case "一致":
            case "有效":
                return "通过";  // 一致、有效都是通过
            case "不一致":
                return "不通过";  // 不一致都是不通过
            case "不存在":
                return "不存在";  // 不存在
            default:
                return "不存在";  // 默认为不存在
        }
    }
    
    @Override
    public String getBatchTaskStatus() {
        if (isRunning.get()) {
            return String.format("任务运行中 - %s", lastMessage);
        } else {
            return String.format("任务已停止 - %s", lastMessage);
        }
    }
    
    /**
     * 执行车辆道路运输证验证任务
     */
    private void executeRoadTransportTask(String verifyType) {
        try {
            isRunning.set(true);
            processedCount.set(0);
            
            // 获取总数据量
            long total = checkVehicleService.countCheckVehicles();
            totalCount.set(total);
            
            logger.info("开始批量验证车辆道路运输证信息，验证类型: {}, 总数据量: {}", verifyType, total);
            lastMessage = String.format("开始批量验证道路运输证，验证类型: %s, 总数据量: %d", verifyType, total);
            
            long offset = 0;
            int successCount = 0;
            int errorCount = 0;
            
            while (offset < total) {
                try {
                    // 分批查询车辆数据
                    List<CheckVehicle> vehicles = checkVehicleService.selectCheckVehicleListByPage(offset, BATCH_SIZE);
                    
                    if (vehicles.isEmpty()) {
                        break;
                    }
                    
                    logger.info("处理第 {} 批数据，数量: {}", (offset / BATCH_SIZE + 1), vehicles.size());
                    
                    // 处理车辆道路运输证验证
                    successCount += processVehicleRoadTransportVerification(vehicles, verifyType);
                    
                    processedCount.addAndGet(vehicles.size());
                    offset += BATCH_SIZE;
                    
                    // 更新状态消息
                    lastMessage = String.format("已处理: %d/%d, 成功: %d, 失败: %d", 
                        processedCount.get(), total, successCount, errorCount);
                    
                    // 批次间隔
                    if (offset < total) {
                        Thread.sleep(BATCH_INTERVAL);
                    }
                    
                } catch (Exception e) {
                    logger.error("处理批次数据时发生错误", e);
                    errorCount += BATCH_SIZE;
                    offset += BATCH_SIZE;
                }
            }
            
            lastMessage = String.format("批处理完成！总处理: %d, 成功: %d, 失败: %d", 
                total, successCount, errorCount);
            logger.info(lastMessage);
            
        } catch (Exception e) {
            logger.error("批处理任务执行失败", e);
            lastMessage = "批处理任务执行失败: " + e.getMessage();
        } finally {
            isRunning.set(false);
        }
    }
    
    /**
     * 处理车辆道路运输证验证
     */
    private int processVehicleRoadTransportVerification(List<CheckVehicle> vehicles, String verifyType) {
        // 调用车辆道路运输证验证接口
        AjaxResult response = callVehicleRoadTransportVerifyApi(vehicles, verifyType);
        
        if (response != null && response.get("code").equals(200) && response.get("data") != null) {
            // 保存验证结果
            Map<String, Object> data = (Map<String, Object>) response.get("data");
            List<Map<String, Object>> results = (List<Map<String, Object>>) data.get("results");
            int saved = saveVehicleRoadTransportVerifyResults(results, verifyType);
            logger.info("成功保存 {} 条车辆道路运输证{}验证结果", saved, 
                "authenticity".equals(verifyType) ? "真实性" : "有效性");
            return saved;
        } else {
            logger.error("车辆道路运输证{}验证接口调用失败，响应: {}", 
                "authenticity".equals(verifyType) ? "真实性" : "有效性", response);
            return 0;
        }
    }
    
    /**
     * 调用车辆道路运输证验证API
     */
    private AjaxResult callVehicleRoadTransportVerifyApi(List<CheckVehicle> vehicles, String verifyType) {
        try {
            // 构建请求数据
            Map<String, Object> request = new HashMap<>();
            request.put("verifyType", verifyType); // 设置验证类型（authenticity或validity）
            List<Map<String, Object>> vehicleList = new ArrayList<>();
            
            for (CheckVehicle vehicle : vehicles) {
                // 只有拥有道路运输证编号和车牌号的车辆才进行验证
                if (StringUtils.isNotEmpty(vehicle.getRoadCard()) && 
                    StringUtils.isNotEmpty(vehicle.getPlateNo()) &&
                    StringUtils.isNotEmpty(vehicle.getOWNER())) {
                    
                    // 处理日期格式
                    String validEndDate = vehicle.getRoadCardExpiryDate() != null ? 
                        new java.text.SimpleDateFormat("yyyy-MM-dd").format(vehicle.getRoadCardExpiryDate()) : null;
                    
                    Map<String, Object> vehicleData = new HashMap<>();
                    vehicleData.put("transportLicenseNumber", vehicle.getRoadCard());  // 道路运输证编号
                    vehicleData.put("plateNumber", vehicle.getPlateNo());             // 车牌号
                    vehicleData.put("businessName", vehicle.getOWNER());              // 业户名称（使用OWNER字段）
                    vehicleData.put("validEndDate", validEndDate);                   // 道路运输证到期日
                    
                    vehicleList.add(vehicleData);
                }
            }
            
            if (vehicleList.isEmpty()) {
                logger.warn("当前批次没有有效的车辆道路运输证数据");
                return null;
            }
            
            request.put("vehicles", vehicleList);
            
            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            // 调用接口
            ResponseEntity<AjaxResult> response = restTemplate.postForEntity(
                vehicleRoadTransportVerifyUrl, entity, AjaxResult.class);
            
            return response.getBody();
            
        } catch (Exception e) {
            logger.error("调用车辆道路运输证验证接口失败", e);
            return null;
        }
    }
    
    /**
     * 保存车辆道路运输证验证结果
     */
    private int saveVehicleRoadTransportVerifyResults(List<Map<String, Object>> results, String verifyType) {
        if (results == null || results.isEmpty()) {
            return 0;
        }
        
        int savedCount = 0;
        
        for (Map<String, Object> result : results) {
            try {
                CheckType checkType = new CheckType();
                
                // 根据验证类型设置字段值
                String checkStatus = "authenticity".equals(verifyType) ? "真实性验证" : "有效性验证";
                checkType.setCheckStatus(checkStatus);  // 验证类型
                checkType.setDataStatus("道运证");      // 数据类型：道运证
                checkType.setCheckData(String.valueOf(result.get("transportLicenseNumber")));  // 道路运输证编号
                checkType.setCheckName(String.valueOf(result.get("owner") != null ? result.get("owner") : 
                    (result.get("businessName") != null ? result.get("businessName") : "")));  // 业户名称
                
                // hd_result: 将verifyResult转换为对应的结果
                String hdResult = convertVerifyResult(String.valueOf(result.get("verifyResult")));
                checkType.setHdResult(hdResult);
                
                // detail: 只有不通过的情况下才存放message
                if ("不通过".equals(hdResult)) {
                    checkType.setDetail(String.valueOf(result.get("message")));
                } else {
                    checkType.setDetail(null);  // 通过和不存在的情况不存放detail
                }
                
                // 设置创建时间
                checkType.setCreateTime(DateUtils.getNowDate());
                
                // 保存到数据库
                checkTypeService.insertCheckType(checkType);
                savedCount++;
                
            } catch (Exception e) {
                logger.error("保存车辆道路运输证验证结果失败: {}", result, e);
            }
        }
        
        return savedCount;
    }
    
    /**
     * 执行车辆所有证件验证任务（行驶证和道路运输证的真实性和有效性）
     */
    private void executeVehicleAllTask() {
        try {
            isRunning.set(true);
            processedCount.set(0);
            
            // 获取总数据量
            long total = checkVehicleService.countCheckVehicles();
            totalCount.set(total);
            
            logger.info("开始批量验证车辆所有证件信息，总数据量: {}", total);
            lastMessage = String.format("开始批量验证车辆所有证件，总数据量: %d", total);
            
            long offset = 0;
            int totalSuccessCount = 0;
            int totalErrorCount = 0;
            
            while (offset < total) {
                try {
                    // 分批查询车辆数据
                    List<CheckVehicle> vehicles = checkVehicleService.selectCheckVehicleListByPage(offset, BATCH_SIZE);
                    
                    if (vehicles.isEmpty()) {
                        break;
                    }
                    
                    logger.info("处理第 {} 批数据，数量: {}", (offset / BATCH_SIZE + 1), vehicles.size());
                    
                    // 同时进行所有验证
                    int batchSuccessCount = processVehicleAllVerifications(vehicles);
                    totalSuccessCount += batchSuccessCount;
                    
                    processedCount.addAndGet(vehicles.size());
                    offset += BATCH_SIZE;
                    
                    // 更新状态消息
                    lastMessage = String.format("已处理: %d/%d, 成功验证: %d, 失败: %d", 
                        processedCount.get(), total, totalSuccessCount, totalErrorCount);
                    
                    // 批次间隔
                    if (offset < total) {
                        Thread.sleep(BATCH_INTERVAL);
                    }
                    
                } catch (Exception e) {
                    logger.error("处理批次数据时发生错误", e);
                    totalErrorCount += BATCH_SIZE;
                    offset += BATCH_SIZE;
                }
            }
            
            lastMessage = String.format("车辆所有证件验证完成！总处理: %d, 成功: %d, 失败: %d", 
                total, totalSuccessCount, totalErrorCount);
            logger.info(lastMessage);
            
        } catch (Exception e) {
            logger.error("车辆所有证件验证任务执行失败", e);
            lastMessage = "车辆所有证件验证任务执行失败: " + e.getMessage();
        } finally {
            isRunning.set(false);
        }
    }
    
    /**
     * 处理车辆所有证件验证（在一次遍历中完成所有验证类型）
     */
    private int processVehicleAllVerifications(List<CheckVehicle> vehicles) {
        int totalSuccessCount = 0;
        
        // 1. 行驶证真实性验证
        totalSuccessCount += processVehicleDrivingLicenseVerification(vehicles, "authenticity");
        
        // 2. 行驶证有效性验证  
        totalSuccessCount += processVehicleDrivingLicenseVerification(vehicles, "validity");
        
        // 3. 道路运输证真实性验证
        totalSuccessCount += processVehicleRoadTransportVerification(vehicles, "authenticity");
        
        // 4. 道路运输证有效性验证
        totalSuccessCount += processVehicleRoadTransportVerification(vehicles, "validity");
        
        logger.info("当前批次完成所有验证，共保存 {} 条验证结果", totalSuccessCount);
        
        return totalSuccessCount;
    }
} 