package com.ruoyi.check.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.check.domain.CheckVehicle;
import com.ruoyi.check.domain.CheckType;
import com.ruoyi.check.domain.dto.VehicleDrivingLicenseVerifyRequest;
import com.ruoyi.check.domain.dto.VehicleDrivingLicenseVerifyResponse;
import com.ruoyi.check.domain.dto.VehicleRoadTransportVerifyRequest;
import com.ruoyi.check.domain.dto.VehicleRoadTransportVerifyResponse;
import com.ruoyi.check.service.ICheckVehicleService;
import com.ruoyi.check.service.ICheckTypeService;
import com.ruoyi.check.service.IVehicleVerifyBatchService;
import com.ruoyi.check.utils.AESUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 车辆验证批处理服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class VehicleVerifyBatchServiceImpl implements IVehicleVerifyBatchService {
    
    private static final Logger logger = LoggerFactory.getLogger(VehicleVerifyBatchServiceImpl.class);
    
    @Autowired
    private ICheckVehicleService checkVehicleService;
    
    @Autowired
    private ICheckTypeService checkTypeService;
    
    @Autowired
    private RestTemplate restTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 每批处理的数量
    private static final int BATCH_SIZE = 200;

    // 批次间隔时间（毫秒）
    private static final long BATCH_INTERVAL = 2000;

    // 车辆行驶证验证接口URL
    @Value("${vehicle.verify.drivinglicense.url:http://localhost:8080/api/v1/huoda/vehicle/verify/drivinglicense}")
    private String vehicleDrivingLicenseVerifyUrl;

    // 车辆道路运输证验证接口URL
    @Value("${vehicle.verify.roadtransport.url:http://localhost:8080/api/v1/huoda/vehicle/verify/roadtransport}")
    private String vehicleRoadTransportVerifyUrl;

    // 是否使用加密传输
    @Value("${vehicle.verify.use.encryption:true}")
    private boolean useEncryption;

    // 加密接口URL
    @Value("${vehicle.verify.encrypted.drivinglicense.url:http://localhost:8080/api/v1/huoda/encrypted/vehicle/verify/drivinglicense}")
    private String encryptedVehicleDrivingLicenseVerifyUrl;

    @Value("${vehicle.verify.encrypted.roadtransport.url:http://localhost:8080/api/v1/huoda/encrypted/vehicle/verify/roadtransport}")
    private String encryptedVehicleRoadTransportVerifyUrl;
    
    // 任务状态
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong totalCount = new AtomicLong(0);
    private String lastMessage = "未开始执行";
    
    @Override
    public String batchVerifyVehicleDrivingLicense() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("authenticity")).start();
        
        return "车辆行驶证真实性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyVehicleDrivingLicenseValidity() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeTask("validity")).start();
        
        return "车辆行驶证有效性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyVehicleRoadTransport() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeRoadTransportTask("authenticity")).start();
        
        return "车辆道路运输证真实性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyVehicleRoadTransportValidity() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeRoadTransportTask("validity")).start();
        
        return "车辆道路运输证有效性验证批处理任务已启动，正在后台执行";
    }
    
    @Override
    public String batchVerifyVehicleAll() {
        if (isRunning.get()) {
            return "批处理任务正在执行中，请稍后再试";
        }
        
        // 启动异步任务
        new Thread(() -> executeVehicleAllTask()).start();
        
        return "车辆所有证件验证批处理任务已启动，正在后台执行";
    }
    
    private void executeTask(String verifyType) {
        try {
            isRunning.set(true);
            processedCount.set(0);
            
            // 获取总数据量
            long total = checkVehicleService.countCheckVehicles();
            totalCount.set(total);
            
            logger.info("开始批量验证车辆行驶证信息，验证类型: {}, 总数据量: {}", verifyType, total);
            lastMessage = String.format("开始批量验证，验证类型: %s, 总数据量: %d", verifyType, total);
            
            long offset = 0;
            int successCount = 0;
            int errorCount = 0;
            
            while (offset < total) {
                try {
                    // 分批查询车辆数据
                    List<CheckVehicle> vehicles = checkVehicleService.selectCheckVehicleListByPage(offset, BATCH_SIZE);
                    
                    if (vehicles.isEmpty()) {
                        break;
                    }
                    
                    logger.info("处理第 {} 批数据，数量: {}", (offset / BATCH_SIZE + 1), vehicles.size());
                    
                    // 处理车辆行驶证验证
                    successCount += processVehicleDrivingLicenseVerification(vehicles, verifyType);
                    
                    processedCount.addAndGet(vehicles.size());
                    offset += BATCH_SIZE;
                    
                    // 更新状态消息
                    lastMessage = String.format("已处理: %d/%d, 成功: %d, 失败: %d", 
                        processedCount.get(), total, successCount, errorCount);
                    
                    // 批次间隔
                    if (offset < total) {
                        Thread.sleep(BATCH_INTERVAL);
                    }
                    
                } catch (Exception e) {
                    logger.error("处理批次数据时发生错误", e);
                    errorCount += BATCH_SIZE;
                    offset += BATCH_SIZE;
                }
            }
            
            lastMessage = String.format("批处理完成！总处理: %d, 成功: %d, 失败: %d", 
                total, successCount, errorCount);
            logger.info(lastMessage);
            
        } catch (Exception e) {
            logger.error("批处理任务执行失败", e);
            lastMessage = "批处理任务执行失败: " + e.getMessage();
        } finally {
            isRunning.set(false);
        }
    }
    
    private int processVehicleDrivingLicenseVerification(List<CheckVehicle> vehicles, String verifyType) {
        // 调用车辆行驶证验证接口
        VehicleDrivingLicenseVerifyResponse response = callVehicleDrivingLicenseVerifyApi(vehicles, verifyType);

        if (response != null) {
            List<VehicleDrivingLicenseVerifyResponse.VehicleResult> results = null;

            // 判断是否为加密接口返回的数据结构（直接包含results字段）
            if (response.getResults() != null) {
                // 加密接口返回的数据结构
                results = response.getResults();
                logger.info("处理加密接口返回的车辆行驶证验证结果，共 {} 条", results.size());
            } else if (response.getCode() != null && response.getCode() == 200 && response.getData() != null) {
                // 非加密接口返回的数据结构
                results = response.getData().getResults();
                logger.info("处理非加密接口返回的车辆行驶证验证结果，共 {} 条", results != null ? results.size() : 0);
            }

            if (results != null && !results.isEmpty()) {
                // 保存验证结果
                int saved = saveVehicleDrivingLicenseVerifyResults(results, verifyType);
                logger.info("成功保存 {} 条车辆行驶证{}验证结果", saved,
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return saved;
            } else {
                logger.warn("车辆行驶证{}验证接口返回空结果",
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return 0;
            }
        } else {
            logger.error("车辆行驶证{}验证接口调用失败，响应为null",
                "authenticity".equals(verifyType) ? "真实性" : "有效性");
            return 0;
        }
    }
    
    private VehicleDrivingLicenseVerifyResponse callVehicleDrivingLicenseVerifyApi(List<CheckVehicle> vehicles, String verifyType) {
        try {
            // 构建请求数据
            VehicleDrivingLicenseVerifyRequest request = new VehicleDrivingLicenseVerifyRequest();
            request.setVerifyType(verifyType); // 设置验证类型（authenticity或validity）
            List<VehicleDrivingLicenseVerifyRequest.VehicleData> vehicleList = new ArrayList<>();

            for (CheckVehicle vehicle : vehicles) {
                // 只有拥有车牌号和车辆识别代码的车辆才进行验证
                if (StringUtils.isNotEmpty(vehicle.getPlateNo()) &&
                    StringUtils.isNotEmpty(vehicle.getCODE()) &&
                    StringUtils.isNotEmpty(vehicle.getOWNER())) {

                    // 处理日期格式
                    String validEndDate = vehicle.getIssueExpiryDate() != null ?
                        new java.text.SimpleDateFormat("yyyy-MM-dd").format(vehicle.getIssueExpiryDate()) : null;

                    VehicleDrivingLicenseVerifyRequest.VehicleData vehicleData = new VehicleDrivingLicenseVerifyRequest.VehicleData(
                        vehicle.getPlateNo(),
                        vehicle.getCODE(),
                        vehicle.getVclTpNm(),
                        vehicle.getOWNER(),
                        validEndDate
                    );
                    vehicleList.add(vehicleData);
                }
            }

            if (vehicleList.isEmpty()) {
                logger.warn("当前批次没有有效的车辆行驶证数据");
                return null;
            }

            request.setVehicles(vehicleList);

            // 根据配置选择加密或非加密调用
            if (useEncryption) {
                logger.info("使用加密方式调用车辆行驶证验证接口");
                try {
                    return callEncryptedVehicleDrivingLicenseVerifyApi(request);
                } catch (Exception e) {
                    logger.error("调用加密车辆行驶证验证接口失败", e);
                    return null;  // 返回null而不是抛出异常
                }
            } else {
                logger.info("使用非加密方式调用车辆行驶证验证接口");
                // 设置HTTP头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);

                HttpEntity<VehicleDrivingLicenseVerifyRequest> entity = new HttpEntity<>(request, headers);

                // 调用接口
                ResponseEntity<VehicleDrivingLicenseVerifyResponse> response = restTemplate.postForEntity(
                    vehicleDrivingLicenseVerifyUrl, entity, VehicleDrivingLicenseVerifyResponse.class);

                return response.getBody();
            }

        } catch (Exception e) {
            logger.error("调用车辆行驶证验证接口失败", e);
            return null;
        }
    }
    
    private int saveVehicleDrivingLicenseVerifyResults(List<VehicleDrivingLicenseVerifyResponse.VehicleResult> results, String verifyType) {
        if (results == null || results.isEmpty()) {
            return 0;
        }

        int savedCount = 0;

        for (VehicleDrivingLicenseVerifyResponse.VehicleResult result : results) {
            try {
                CheckType checkType = new CheckType();

                // 根据验证类型设置字段值
                String checkStatus = "authenticity".equals(verifyType) ? "真实性验证" : "有效性验证";
                checkType.setCheckStatus(checkStatus);  // 验证类型
                checkType.setDataStatus("行驶证");      // 数据类型：行驶证
                checkType.setCheckData(result.getPlateNumber());  // 验证的数据（车牌号）
                checkType.setCheckName(result.getOwner());        // 车主姓名

                // hd_result: 将verifyResult转换为对应的结果
                String hdResult = convertVerifyResult(result.getVerifyResult());
                checkType.setHdResult(hdResult);

                // detail: 只有不通过的情况下才存放message
                if ("不通过".equals(hdResult)) {
                    checkType.setDetail(result.getMessage());
                } else {
                    checkType.setDetail(null);  // 通过和不存在的情况不存放detail
                }

                // 设置创建时间
                checkType.setCreateTime(DateUtils.getNowDate());

                // 保存到数据库
                checkTypeService.insertCheckType(checkType);
                savedCount++;

            } catch (Exception e) {
                logger.error("保存车辆行驶证验证结果失败: {}", result, e);
            }
        }

        return savedCount;
    }
    
    /**
     * 将验证结果转换为对应的文字描述
     * @param verifyResult 验证结果
     * @return 对应的结果描述
     */
    private String convertVerifyResult(String verifyResult) {
        if (StringUtils.isEmpty(verifyResult)) {
            return "不存在";  // 默认为不存在
        }
        
        switch (verifyResult) {
            case "一致":
            case "有效":
                return "通过";  // 一致、有效都是通过
            case "不一致":
                return "不通过";  // 不一致都是不通过
            case "不存在":
                return "不存在";  // 不存在
            default:
                return "不存在";  // 默认为不存在
        }
    }
    
    @Override
    public String getBatchTaskStatus() {
        if (isRunning.get()) {
            return String.format("任务运行中 - %s", lastMessage);
        } else {
            return String.format("任务已停止 - %s", lastMessage);
        }
    }
    
    /**
     * 执行车辆道路运输证验证任务
     */
    private void executeRoadTransportTask(String verifyType) {
        try {
            isRunning.set(true);
            processedCount.set(0);
            
            // 获取总数据量
            long total = checkVehicleService.countCheckVehicles();
            totalCount.set(total);
            
            logger.info("开始批量验证车辆道路运输证信息，验证类型: {}, 总数据量: {}", verifyType, total);
            lastMessage = String.format("开始批量验证道路运输证，验证类型: %s, 总数据量: %d", verifyType, total);
            
            long offset = 0;
            int successCount = 0;
            int errorCount = 0;
            
            while (offset < total) {
                try {
                    // 分批查询车辆数据
                    List<CheckVehicle> vehicles = checkVehicleService.selectCheckVehicleListByPage(offset, BATCH_SIZE);
                    
                    if (vehicles.isEmpty()) {
                        break;
                    }
                    
                    logger.info("处理第 {} 批数据，数量: {}", (offset / BATCH_SIZE + 1), vehicles.size());
                    
                    // 处理车辆道路运输证验证
                    successCount += processVehicleRoadTransportVerification(vehicles, verifyType);
                    
                    processedCount.addAndGet(vehicles.size());
                    offset += BATCH_SIZE;
                    
                    // 更新状态消息
                    lastMessage = String.format("已处理: %d/%d, 成功: %d, 失败: %d", 
                        processedCount.get(), total, successCount, errorCount);
                    
                    // 批次间隔
                    if (offset < total) {
                        Thread.sleep(BATCH_INTERVAL);
                    }
                    
                } catch (Exception e) {
                    logger.error("处理批次数据时发生错误", e);
                    errorCount += BATCH_SIZE;
                    offset += BATCH_SIZE;
                }
            }
            
            lastMessage = String.format("批处理完成！总处理: %d, 成功: %d, 失败: %d", 
                total, successCount, errorCount);
            logger.info(lastMessage);
            
        } catch (Exception e) {
            logger.error("批处理任务执行失败", e);
            lastMessage = "批处理任务执行失败: " + e.getMessage();
        } finally {
            isRunning.set(false);
        }
    }
    
    /**
     * 处理车辆道路运输证验证
     */
    private int processVehicleRoadTransportVerification(List<CheckVehicle> vehicles, String verifyType) {
        // 调用车辆道路运输证验证接口
        VehicleRoadTransportVerifyResponse response = callVehicleRoadTransportVerifyApi(vehicles, verifyType);

        if (response != null) {
            List<VehicleRoadTransportVerifyResponse.VehicleResult> results = null;

            // 判断是否为加密接口返回的数据结构（直接包含results字段）
            if (response.getResults() != null) {
                // 加密接口返回的数据结构
                results = response.getResults();
                logger.info("处理加密接口返回的车辆道路运输证验证结果，共 {} 条", results.size());
            } else if (response.getCode() != null && response.getCode() == 200 && response.getData() != null) {
                // 非加密接口返回的数据结构
                results = response.getData().getResults();
                logger.info("处理非加密接口返回的车辆道路运输证验证结果，共 {} 条", results != null ? results.size() : 0);
            }

            if (results != null && !results.isEmpty()) {
                // 保存验证结果
                int saved = saveVehicleRoadTransportVerifyResults(results, verifyType);
                logger.info("成功保存 {} 条车辆道路运输证{}验证结果", saved,
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return saved;
            } else {
                logger.warn("车辆道路运输证{}验证接口返回空结果",
                    "authenticity".equals(verifyType) ? "真实性" : "有效性");
                return 0;
            }
        } else {
            logger.error("车辆道路运输证{}验证接口调用失败，响应为null",
                "authenticity".equals(verifyType) ? "真实性" : "有效性");
            return 0;
        }
    }
    
    /**
     * 调用车辆道路运输证验证API
     */
    private VehicleRoadTransportVerifyResponse callVehicleRoadTransportVerifyApi(List<CheckVehicle> vehicles, String verifyType) {
        try {
            // 构建请求数据
            VehicleRoadTransportVerifyRequest request = new VehicleRoadTransportVerifyRequest();
            request.setVerifyType(verifyType); // 设置验证类型（authenticity或validity）
            List<VehicleRoadTransportVerifyRequest.VehicleData> vehicleList = new ArrayList<>();

            for (CheckVehicle vehicle : vehicles) {
                // 只有拥有道路运输证编号和车牌号的车辆才进行验证
                if (StringUtils.isNotEmpty(vehicle.getRoadCard()) &&
                    StringUtils.isNotEmpty(vehicle.getPlateNo()) &&
                    StringUtils.isNotEmpty(vehicle.getOWNER())) {

                    // 处理日期格式
                    String validEndDate = vehicle.getRoadCardExpiryDate() != null ?
                        new java.text.SimpleDateFormat("yyyy-MM-dd").format(vehicle.getRoadCardExpiryDate()) : null;

                    VehicleRoadTransportVerifyRequest.VehicleData vehicleData = new VehicleRoadTransportVerifyRequest.VehicleData(
                        vehicle.getRoadCard(),  // 道路运输证编号
                        vehicle.getPlateNo(),   // 车牌号
                        vehicle.getOWNER(),     // 业户名称（使用OWNER字段）
                        validEndDate            // 道路运输证到期日
                    );
                    vehicleList.add(vehicleData);
                }
            }

            if (vehicleList.isEmpty()) {
                logger.warn("当前批次没有有效的车辆道路运输证数据");
                return null;
            }

            request.setVehicles(vehicleList);

            // 根据配置选择加密或非加密调用
            if (useEncryption) {
                logger.info("使用加密方式调用车辆道路运输证验证接口");
                try {
                    return callEncryptedVehicleRoadTransportVerifyApi(request);
                } catch (Exception e) {
                    logger.error("调用加密车辆道路运输证验证接口失败", e);
                    return null;  // 返回null而不是抛出异常
                }
            } else {
                logger.info("使用非加密方式调用车辆道路运输证验证接口");
                // 设置HTTP头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);

                HttpEntity<VehicleRoadTransportVerifyRequest> entity = new HttpEntity<>(request, headers);

                // 调用接口
                ResponseEntity<VehicleRoadTransportVerifyResponse> response = restTemplate.postForEntity(
                    vehicleRoadTransportVerifyUrl, entity, VehicleRoadTransportVerifyResponse.class);

                return response.getBody();
            }

        } catch (Exception e) {
            logger.error("调用车辆道路运输证验证接口失败", e);
            return null;
        }
    }
    
    /**
     * 保存车辆道路运输证验证结果
     */
    private int saveVehicleRoadTransportVerifyResults(List<VehicleRoadTransportVerifyResponse.VehicleResult> results, String verifyType) {
        if (results == null || results.isEmpty()) {
            return 0;
        }

        int savedCount = 0;

        for (VehicleRoadTransportVerifyResponse.VehicleResult result : results) {
            try {
                CheckType checkType = new CheckType();

                // 根据验证类型设置字段值
                String checkStatus = "authenticity".equals(verifyType) ? "真实性验证" : "有效性验证";
                checkType.setCheckStatus(checkStatus);  // 验证类型
                checkType.setDataStatus("道运证");      // 数据类型：道运证
                checkType.setCheckData(result.getTransportLicenseNumber());  // 道路运输证编号
                checkType.setCheckName(result.getOwner() != null ? result.getOwner() :
                    (result.getBusinessName() != null ? result.getBusinessName() : ""));  // 业户名称

                // hd_result: 将verifyResult转换为对应的结果
                String hdResult = convertVerifyResult(result.getVerifyResult());
                checkType.setHdResult(hdResult);

                // detail: 只有不通过的情况下才存放message
                if ("不通过".equals(hdResult)) {
                    checkType.setDetail(result.getMessage());
                } else {
                    checkType.setDetail(null);  // 通过和不存在的情况不存放detail
                }

                // 设置创建时间
                checkType.setCreateTime(DateUtils.getNowDate());

                // 保存到数据库
                checkTypeService.insertCheckType(checkType);
                savedCount++;

            } catch (Exception e) {
                logger.error("保存车辆道路运输证验证结果失败: {}", result, e);
            }
        }

        return savedCount;
    }
    
    /**
     * 执行车辆所有证件验证任务（行驶证和道路运输证的真实性和有效性）
     */
    private void executeVehicleAllTask() {
        try {
            isRunning.set(true);
            processedCount.set(0);
            
            // 获取总数据量
            long total = checkVehicleService.countCheckVehicles();
            totalCount.set(total);
            
            logger.info("开始批量验证车辆所有证件信息，总数据量: {}", total);
            lastMessage = String.format("开始批量验证车辆所有证件，总数据量: %d", total);
            
            long offset = 0;
            int totalSuccessCount = 0;
            int totalErrorCount = 0;
            
            while (offset < total) {
                try {
                    // 分批查询车辆数据
                    List<CheckVehicle> vehicles = checkVehicleService.selectCheckVehicleListByPage(offset, BATCH_SIZE);
                    
                    if (vehicles.isEmpty()) {
                        break;
                    }
                    
                    logger.info("处理第 {} 批数据，数量: {}", (offset / BATCH_SIZE + 1), vehicles.size());
                    
                    // 同时进行所有验证
                    int batchSuccessCount = processVehicleAllVerifications(vehicles);
                    totalSuccessCount += batchSuccessCount;
                    
                    processedCount.addAndGet(vehicles.size());
                    offset += BATCH_SIZE;
                    
                    // 更新状态消息
                    lastMessage = String.format("已处理: %d/%d, 成功验证: %d, 失败: %d", 
                        processedCount.get(), total, totalSuccessCount, totalErrorCount);
                    
                    // 批次间隔
                    if (offset < total) {
                        Thread.sleep(BATCH_INTERVAL);
                    }
                    
                } catch (Exception e) {
                    logger.error("处理批次数据时发生错误", e);
                    totalErrorCount += BATCH_SIZE;
                    offset += BATCH_SIZE;
                }
            }
            
            lastMessage = String.format("车辆所有证件验证完成！总处理: %d, 成功: %d, 失败: %d", 
                total, totalSuccessCount, totalErrorCount);
            logger.info(lastMessage);
            
        } catch (Exception e) {
            logger.error("车辆所有证件验证任务执行失败", e);
            lastMessage = "车辆所有证件验证任务执行失败: " + e.getMessage();
        } finally {
            isRunning.set(false);
        }
    }
    
    /**
     * 处理车辆所有证件验证（在一次遍历中完成所有验证类型）
     */
    private int processVehicleAllVerifications(List<CheckVehicle> vehicles) {
        int totalSuccessCount = 0;
        
        // 1. 行驶证真实性验证
        totalSuccessCount += processVehicleDrivingLicenseVerification(vehicles, "authenticity");
        
        // 2. 行驶证有效性验证  
        totalSuccessCount += processVehicleDrivingLicenseVerification(vehicles, "validity");
        
        // 3. 道路运输证真实性验证
        totalSuccessCount += processVehicleRoadTransportVerification(vehicles, "authenticity");
        
        // 4. 道路运输证有效性验证
        totalSuccessCount += processVehicleRoadTransportVerification(vehicles, "validity");
        
        logger.info("当前批次完成所有验证，共保存 {} 条验证结果", totalSuccessCount);

        return totalSuccessCount;
    }

    /**
     * 调用加密车辆行驶证验证接口
     */
    private VehicleDrivingLicenseVerifyResponse callEncryptedVehicleDrivingLicenseVerifyApi(VehicleDrivingLicenseVerifyRequest request) {
        try {
            // 1. 序列化请求数据
            String requestJson = objectMapper.writeValueAsString(request);

            // 2. 加密请求数据
            String encryptedData = AESUtils.encrypt(requestJson);

            // 3. 构建加密请求包装
            Map<String, Object> encryptedRequest = new HashMap<>();
            encryptedRequest.put("encryptedData", encryptedData);
            encryptedRequest.put("timestamp", System.currentTimeMillis());

            // 4. 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);

            // 5. 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(encryptedVehicleDrivingLicenseVerifyUrl, entity, Map.class);

            logger.info("车辆行驶证加密接口响应状态: {}", response.getStatusCode());
            logger.info("车辆行驶证加密接口响应体: {}", response.getBody());

            if (response.getBody() != null && response.getBody().get("code") != null && response.getBody().get("code").equals(200)) {
                // 6. 提取加密响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) response.getBody().get("data");

                if (responseData == null) {
                    logger.error("车辆行驶证验证响应中的data字段为null");
                    return null;
                }

                String encryptedResponseData = (String) responseData.get("encryptedData");

                if (encryptedResponseData == null) {
                    logger.error("车辆行驶证验证响应中的encryptedData字段为null，responseData内容: {}", responseData);
                    return null;
                }

                // 7. 解密响应数据
                String decryptedResponse = AESUtils.decrypt(encryptedResponseData);

                // 8. 反序列化为目标对象
                return objectMapper.readValue(decryptedResponse, VehicleDrivingLicenseVerifyResponse.class);
            } else {
                logger.error("车辆行驶证加密API调用失败，响应: {}", response.getBody());
                return null;
            }

        } catch (Exception e) {
            logger.error("调用加密车辆行驶证验证接口失败", e);
            return null;
        }
    }

    /**
     * 调用加密车辆道路运输证验证接口
     */
    private VehicleRoadTransportVerifyResponse callEncryptedVehicleRoadTransportVerifyApi(VehicleRoadTransportVerifyRequest request) {
        try {
            // 1. 序列化请求数据
            String requestJson = objectMapper.writeValueAsString(request);

            // 2. 加密请求数据
            String encryptedData = AESUtils.encrypt(requestJson);

            // 3. 构建加密请求包装
            Map<String, Object> encryptedRequest = new HashMap<>();
            encryptedRequest.put("encryptedData", encryptedData);
            encryptedRequest.put("timestamp", System.currentTimeMillis());

            // 4. 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(encryptedRequest, headers);

            // 5. 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(encryptedVehicleRoadTransportVerifyUrl, entity, Map.class);

            logger.info("车辆道路运输证加密接口响应状态: {}", response.getStatusCode());
            logger.info("车辆道路运输证加密接口响应体: {}", response.getBody());

            if (response.getBody() != null && response.getBody().get("code") != null && response.getBody().get("code").equals(200)) {
                // 6. 提取加密响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) response.getBody().get("data");

                if (responseData == null) {
                    logger.error("车辆道路运输证验证响应中的data字段为null");
                    return null;
                }

                String encryptedResponseData = (String) responseData.get("encryptedData");

                if (encryptedResponseData == null) {
                    logger.error("车辆道路运输证验证响应中的encryptedData字段为null，responseData内容: {}", responseData);
                    return null;
                }

                // 7. 解密响应数据
                String decryptedResponse = AESUtils.decrypt(encryptedResponseData);

                // 8. 反序列化为目标对象
                return objectMapper.readValue(decryptedResponse, VehicleRoadTransportVerifyResponse.class);
            } else {
                logger.error("车辆道路运输证加密API调用失败，响应: {}", response.getBody());
                return null;
            }

        } catch (Exception e) {
            logger.error("调用加密车辆道路运输证验证接口失败", e);
            return null;
        }
    }
}