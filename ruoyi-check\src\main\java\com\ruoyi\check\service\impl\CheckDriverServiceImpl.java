package com.ruoyi.check.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.check.mapper.CheckDriverMapper;
import com.ruoyi.check.domain.CheckDriver;
import com.ruoyi.check.service.ICheckDriverService;

/**
 * 司机Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class CheckDriverServiceImpl implements ICheckDriverService 
{
    @Autowired
    private CheckDriverMapper checkDriverMapper;

    /**
     * 查询司机
     * 
     * @param driverId 司机主键
     * @return 司机
     */
    @Override
    public CheckDriver selectCheckDriverByDriverId(Long driverId)
    {
        return checkDriverMapper.selectCheckDriverByDriverId(driverId);
    }

    /**
     * 查询司机列表
     * 
     * @param checkDriver 司机
     * @return 司机
     */
    @Override
    public List<CheckDriver> selectCheckDriverList(CheckDriver checkDriver)
    {
        return checkDriverMapper.selectCheckDriverList(checkDriver);
    }

    /**
     * 新增司机
     * 
     * @param checkDriver 司机
     * @return 结果
     */
    @Override
    public int insertCheckDriver(CheckDriver checkDriver)
    {
        checkDriver.setCreateTime(DateUtils.getNowDate());
        return checkDriverMapper.insertCheckDriver(checkDriver);
    }

    /**
     * 修改司机
     * 
     * @param checkDriver 司机
     * @return 结果
     */
    @Override
    public int updateCheckDriver(CheckDriver checkDriver)
    {
        checkDriver.setUpdateTime(DateUtils.getNowDate());
        return checkDriverMapper.updateCheckDriver(checkDriver);
    }

    /**
     * 批量删除司机
     * 
     * @param driverIds 需要删除的司机主键
     * @return 结果
     */
    @Override
    public int deleteCheckDriverByDriverIds(Long[] driverIds)
    {
        return checkDriverMapper.deleteCheckDriverByDriverIds(driverIds);
    }

    /**
     * 删除司机信息
     * 
     * @param driverId 司机主键
     * @return 结果
     */
    @Override
    public int deleteCheckDriverByDriverId(Long driverId)
    {
        return checkDriverMapper.deleteCheckDriverByDriverId(driverId);
    }
    
    /**
     * 分页查询司机列表（用于批量处理）
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 司机集合
     */
    @Override
    public List<CheckDriver> selectCheckDriverListByPage(Long offset, Integer limit)
    {
        return checkDriverMapper.selectCheckDriverListByPage(offset, limit);
    }
    
    /**
     * 统计司机总数
     * 
     * @return 总数
     */
    @Override
    public long countCheckDrivers()
    {
        return checkDriverMapper.countCheckDrivers();
    }
}
