package com.ruoyi.check.domain;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车辆行驶证验证请求对象
 */
@ApiModel(value = "VehicleDrivingLicenseVerifyRequest", description = "车辆行驶证验证请求")
public class VehicleDrivingLicenseVerifyRequest {
    
    @ApiModelProperty(value = "验证类型：authenticity（真实性）| validity（有效性）", required = true, example = "authenticity")
    private String verifyType;  // 验证类型：authenticity（真实性）| validity（有效性）
    
    @ApiModelProperty(value = "车辆行驶证验证数据列表", required = true)
    private List<VehicleDrivingLicenseData> vehicles;
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<VehicleDrivingLicenseData> getVehicles() {
        return vehicles;
    }
    
    public void setVehicles(List<VehicleDrivingLicenseData> vehicles) {
        this.vehicles = vehicles;
    }
    
    @ApiModel(value = "VehicleDrivingLicenseData", description = "车辆行驶证验证数据")
    public static class VehicleDrivingLicenseData {
        @ApiModelProperty(value = "车牌号（明文）", required = true, example = "陕A12345")
        private String plateNumber;                    // 车牌号（明文）
        
        @ApiModelProperty(value = "车辆识别代码", required = true, example = "LSGGG54X8DS123456")
        private String vehicleIdentificationCode;      // 车辆识别代码
        
        @ApiModelProperty(value = "车辆类型", required = true, example = "货车")
        private String vehicleType;                    // 车辆类型
        
        @ApiModelProperty(value = "所有人", required = true, example = "西安市货运有限公司")
        private String owner;                          // 所有人
        
        @ApiModelProperty(value = "行驶证到期日期", required = true, example = "2025-12-31")
        private String validEndDate;                   // 行驶证到期日期
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getVehicleIdentificationCode() {
            return vehicleIdentificationCode;
        }
        
        public void setVehicleIdentificationCode(String vehicleIdentificationCode) {
            this.vehicleIdentificationCode = vehicleIdentificationCode;
        }
        
        public String getVehicleType() {
            return vehicleType;
        }
        
        public void setVehicleType(String vehicleType) {
            this.vehicleType = vehicleType;
        }
        
        public String getOwner() {
            return owner;
        }
        
        public void setOwner(String owner) {
            this.owner = owner;
        }
        
        public String getValidEndDate() {
            return validEndDate;
        }
        
        public void setValidEndDate(String validEndDate) {
            this.validEndDate = validEndDate;
        }
    }
} 