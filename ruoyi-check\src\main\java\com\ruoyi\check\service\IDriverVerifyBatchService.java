package com.ruoyi.check.service;

/**
 * 司机验证批处理服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IDriverVerifyBatchService 
{
    /**
     * 批量验证所有司机手机号
     * 执行步骤：
     * 1. 分批查询check_driver表中的司机数据（每批1000条）
     * 2. 调用手机号验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyDriverPhones();
    
    /**
     * 批量验证所有司机身份证（真实性验证）
     * 执行步骤：
     * 1. 分批查询check_driver表中的司机数据（每批1000条）
     * 2. 调用身份证真实性验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyDriverIdCards();
    
    /**
     * 批量验证所有司机身份证（有效性验证）
     * 执行步骤：
     * 1. 分批查询check_driver表中的司机数据（每批1000条）
     * 2. 调用身份证有效性验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyDriverIdCardValidity();
    
    /**
     * 批量验证所有司机驾驶证（真实性验证）
     * 执行步骤：
     * 1. 分批查询check_driver表中的司机数据（每批1000条）
     * 2. 调用驾驶证真实性验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyDriverLicense();
    
    /**
     * 批量验证所有司机驾驶证（有效性验证）
     * 执行步骤：
     * 1. 分批查询check_driver表中的司机数据（每批1000条）
     * 2. 调用驾驶证有效性验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyDriverLicenseValidity();
    
    /**
     * 批量验证所有司机从业资格证（真实性验证）
     * 执行步骤：
     * 1. 分批查询check_driver表中的司机数据（每批1000条）
     * 2. 调用从业资格证真实性验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyDriverQualification();
    
    /**
     * 批量验证所有司机从业资格证（有效性验证）
     * 执行步骤：
     * 1. 分批查询check_driver表中的司机数据（每批1000条）
     * 2. 调用从业资格证有效性验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyDriverQualificationValidity();
    
         /**
      * 批量验证所有司机信息（手机号+身份证+驾驶证+从业资格证）
      * 执行步骤：
      * 1. 分批查询check_driver表中的司机数据（每批1000条）
      * 2. 同时调用手机号、身份证真实性、身份证有效性、驾驶证真实性、驾驶证有效性和从业资格证真实性验证接口
      * 3. 将验证结果保存到check_type表中
      * 
      * @return 处理结果消息
      */
     String batchVerifyDriverAll();
    
    /**
     * 获取批处理任务状态
     * 
     * @return 任务状态信息
     */
    String getBatchTaskStatus();
} 