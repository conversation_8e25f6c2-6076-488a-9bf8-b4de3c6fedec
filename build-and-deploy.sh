#!/bin/bash

# RuoYi项目Docker构建和部署脚本
# 作者: RuoYi Team
# 版本: 2.0 - 支持多阶段构建

set -e

echo "=========================================="
echo "RuoYi项目Docker构建和部署脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_message "Docker环境检查通过"
}

# 检查Maven是否安装（本地构建时需要）
check_maven() {
    if ! command -v mvn &> /dev/null; then
        print_warning "Maven未安装，将使用Docker多阶段构建"
        return 1
    fi
    print_message "Maven环境检查通过"
    return 0
}

# 清理旧的构建文件
clean_build() {
    print_message "清理旧的构建文件..."
    if check_maven; then
        mvn clean
    else
        print_message "跳过本地清理，将在Docker中进行"
    fi
}

# 编译项目（本地）
build_project() {
    print_message "开始本地编译项目..."
    if ! check_maven; then
        print_warning "Maven未安装，跳过本地编译，将在Docker中编译"
        return 0
    fi
    
    mvn package -Dmaven.test.skip=true
    
    if [ ! -f "ruoyi-admin/target/ruoyi-admin.jar" ]; then
        print_error "本地编译失败，未找到jar包"
        exit 1
    fi
    
    print_message "本地项目编译成功"
}

# 构建Docker镜像（包含编译过程）
build_docker_image() {
    print_message "构建Docker镜像（包含项目编译）..."
    print_message "这可能需要几分钟时间，请耐心等待..."
    
    # 使用多阶段构建，Docker会自动编译项目
    docker build -t ruoyi-app:latest .
    
    if [ $? -eq 0 ]; then
        print_message "Docker镜像构建成功"
    else
        print_error "Docker镜像构建失败"
        exit 1
    fi
}

# 停止现有容器
stop_containers() {
    print_message "停止现有容器..."
    docker-compose down
}

# 启动服务
start_services() {
    print_message "启动服务..."
    docker-compose up -d
    
    print_message "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        print_message "服务启动成功！"
        echo ""
        echo "=========================================="
        echo "部署完成！"
        echo "应用访问地址: http://localhost:8973"
        echo "Swagger文档: http://localhost:8973/swagger-ui/"
        echo "数据库地址: localhost:3306"
        echo "Redis地址: localhost:6379"
        echo "=========================================="
    else
        print_error "服务启动失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 显示日志
show_logs() {
    print_message "显示应用日志..."
    docker-compose logs -f ruoyi-app
}

# 主函数
main() {
    case "$1" in
        "build")
            check_maven
            clean_build
            build_project
            ;;
        "docker")
            check_docker
            build_docker_image
            ;;
        "deploy")
            check_docker
            # 不再强制要求Maven，因为Docker可以自己构建
            build_docker_image
            stop_containers
            start_services
            ;;
        "start")
            check_docker
            start_services
            ;;
        "stop")
            check_docker
            stop_containers
            ;;
        "restart")
            check_docker
            stop_containers
            start_services
            ;;
        "logs")
            check_docker
            show_logs
            ;;
        *)
            echo "用法: $0 {build|docker|deploy|start|stop|restart|logs}"
            echo ""
            echo "命令说明:"
            echo "  build   - 仅本地编译项目（需要Maven）"
            echo "  docker  - 构建Docker镜像（包含项目编译）"
            echo "  deploy  - 完整部署（构建镜像+启动服务）"
            echo "  start   - 启动服务"
            echo "  stop    - 停止服务"
            echo "  restart - 重启服务"
            echo "  logs    - 查看应用日志"
            echo ""
            echo "注意:"
            echo "  - 现在使用Docker多阶段构建，无需预先安装Maven"
            echo "  - deploy命令会在Docker中自动编译项目"
            echo ""
            echo "示例:"
            echo "  $0 deploy    # 完整部署（推荐）"
            echo "  $0 logs      # 查看日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 