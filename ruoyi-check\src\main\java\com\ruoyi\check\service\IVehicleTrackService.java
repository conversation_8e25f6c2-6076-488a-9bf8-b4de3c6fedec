package com.ruoyi.check.service;

import com.ruoyi.check.controller.VehicleTrackQueryRequest;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
 * 车辆轨迹查询服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IVehicleTrackService {
    
    /**
     * 查询车辆历史轨迹（单个车辆）
     * 
     * @param plateNumber 车牌号
     * @param loadTime 装车时间
     * @param arriveTime 运抵时间
     * @return 轨迹查询结果
     */
    AjaxResult queryVehicleTrackHistory(String plateNumber, String loadTime, String arriveTime);
    
    /**
     * 批量查询车辆历史轨迹
     * 
     * @param vehicles 车辆信息列表
     * @return 轨迹查询结果
     */
    AjaxResult queryVehicleTrackHistoryBatch(List<VehicleTrackQueryRequest.VehicleInfo> vehicles);
} 