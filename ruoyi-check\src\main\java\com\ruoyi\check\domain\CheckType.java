package com.ruoyi.check.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 验证对象 check_type
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public class CheckType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 类型ID */
    private Long typeId;

    /** 验证的数据 */
    @Excel(name = "验证的数据")
    private String checkData;

    /** 名称 */
    @Excel(name = "名称")
    private String checkName;

    /** 验证类型（0一致性验证 1真实性验证 2 有效性验证） */
    @Excel(name = "验证类型")
    private String checkStatus;

    /** 数据类型 */
    @Excel(name = "数据类型")
    private String dataStatus;

    /** 货达验证结果 */
    @Excel(name = "货达验证结果")
    private String hdResult;

    /** 和硕验证结果*/
    @Excel(name = "和硕验证结果")
    private String hsResult;

    /** 物泊验证结果 */
    @Excel(name = "物泊验证结果")
    private String wbResult;

    /** 卡一车验证结果 */
    @Excel(name = "卡一车验证结果")
    private String kycResult;

    /** 大道成验证结果 */
    @Excel(name = "大道成验证结果")
    private String ddcResult;

    /** 详情 */
    @Excel(name = "详情")
    private String detail;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setTypeId(Long typeId) 
    {
        this.typeId = typeId;
    }

    public Long getTypeId() 
    {
        return typeId;
    }

    public void setCheckData(String checkData) 
    {
        this.checkData = checkData;
    }

    public String getCheckData() 
    {
        return checkData;
    }

    public void setCheckName(String checkName) 
    {
        this.checkName = checkName;
    }

    public String getCheckName() 
    {
        return checkName;
    }

    public void setCheckStatus(String checkStatus) 
    {
        this.checkStatus = checkStatus;
    }

    public String getCheckStatus() 
    {
        return checkStatus;
    }

    public void setDataStatus(String dataStatus) 
    {
        this.dataStatus = dataStatus;
    }

    public String getDataStatus() 
    {
        return dataStatus;
    }

    public void setHdResult(String hdResult) 
    {
        this.hdResult = hdResult;
    }

    public String getHdResult() 
    {
        return hdResult;
    }

    public void setHsResult(String hsResult) 
    {
        this.hsResult = hsResult;
    }

    public String getHsResult() 
    {
        return hsResult;
    }

    public void setWbResult(String wbResult) 
    {
        this.wbResult = wbResult;
    }

    public String getWbResult() 
    {
        return wbResult;
    }

    public void setKycResult(String kycResult) 
    {
        this.kycResult = kycResult;
    }

    public String getKycResult() 
    {
        return kycResult;
    }

    public void setDdcResult(String ddcResult) 
    {
        this.ddcResult = ddcResult;
    }

    public String getDdcResult() 
    {
        return ddcResult;
    }

    public void setDetail(String detail) 
    {
        this.detail = detail;
    }

    public String getDetail() 
    {
        return detail;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("typeId", getTypeId())
            .append("checkData", getCheckData())
            .append("checkName", getCheckName())
            .append("checkStatus", getCheckStatus())
            .append("dataStatus", getDataStatus())
            .append("hdResult", getHdResult())
            .append("hsResult", getHsResult())
            .append("wbResult", getWbResult())
            .append("kycResult", getKycResult())
            .append("ddcResult", getDdcResult())
            .append("detail", getDetail())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
