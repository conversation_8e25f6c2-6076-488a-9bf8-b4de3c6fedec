package com.ruoyi.check.service;

/**
 * 车辆验证批处理服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IVehicleVerifyBatchService 
{
    /**
     * 批量验证所有车辆行驶证（真实性验证）
     * 执行步骤：
     * 1. 分批查询check_vehicle表中的车辆数据（每批1000条）
     * 2. 调用行驶证真实性验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyVehicleDrivingLicense();
    
    /**
     * 批量验证所有车辆行驶证（有效性验证）
     * 执行步骤：
     * 1. 分批查询check_vehicle表中的车辆数据（每批1000条）
     * 2. 调用行驶证有效性验证接口进行验证
     * 3. 将验证结果保存到check_type表中
     * 
     * @return 处理结果消息
     */
    String batchVerifyVehicleDrivingLicenseValidity();
    
    /**
     * 批量验证车辆道路运输证真实性
     * 
     * @return 处理结果
     */
    String batchVerifyVehicleRoadTransport();
    
    /**
     * 批量验证车辆道路运输证有效性
     * 
     * @return 处理结果
     */
    String batchVerifyVehicleRoadTransportValidity();
    
    /**
     * 批量验证车辆所有证件（行驶证和道路运输证的真实性和有效性）
     * 
     * @return 处理结果
     */
    String batchVerifyVehicleAll();
    
    /**
     * 获取批处理任务状态
     * 
     * @return 任务状态信息
     */
    String getBatchTaskStatus();
} 