package com.ruoyi.check.controller;

import com.ruoyi.check.domain.*;
import com.ruoyi.check.service.DriverVerifyService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

/**
 * 司机验证控制器
 */
@Api(tags = "货达Mock服务-司机验证接口", description = "提供司机相关证件的批量验证服务")
@RestController
@RequestMapping("/api/v1/huoda/driver/verify")
public class DriverVerifyController extends BaseController {
    
    @Autowired
    private DriverVerifyService driverVerifyService;
    
    /**
     * 手机号码批量验证接口
     */
    @ApiOperation(value = "手机号码批量验证", 
                  notes = "验证手机号码与身份证、姓名的一致性。\n\n" +
                         "请求示例：\n" +
                         "```json\n" +
                         "{\n" +
                         "  \"phones\": [\n" +
                         "    {\n" +
                         "      \"phone\": \"13800138000\",\n" +
                         "      \"name\": \"李四\"\n" +
                         "    },\n" +
                         "    {\n" +
                         "      \"phone\": \"13900139000\",\n" +
                         "      \"name\": \"张三\"\n" +
                         "    }\n" +
                         "  ]\n" +
                         "}\n" +
                         "```")
    @ApiImplicitParam(name = "request", value = "手机号验证请求参数", required = true, dataTypeClass = DriverPhoneVerifyRequest.class)
    @Anonymous
    @PostMapping("/phone")
    public AjaxResult verifyPhone(@RequestBody DriverPhoneVerifyRequest request) {
        VerifyResponseData responseData = driverVerifyService.verifyPhone(request);
        return AjaxResult.success("请求处理成功", responseData);
    }
    
    /**
     * 身份证批量验证接口
     */
    @ApiOperation(value = "身份证批量验证", 
                  notes = "验证身份证号码与姓名的一致性，支持真实性和有效性验证。\n\n" +
                         "请求示例：\n" +
                         "```json\n" +
                         "{\n" +
                         "  \"verifyType\": \"authenticity\",\n" +
                         "  \"idCards\": [\n" +
                         "    {\n" +
                         "      \"idCard\": \"6105281122111138\",\n" +
                         "      \"name\": \"李四\"\n" +
                         "    },\n" +
                         "    {\n" +
                         "      \"idCard\": \"6105291122222222\",\n" +
                         "      \"name\": \"张三\"\n" +
                         "    }\n" +
                         "  ]\n" +
                         "}\n" +
                         "```")
    @ApiImplicitParam(name = "request", value = "身份证验证请求参数", required = true, dataTypeClass = DriverIdCardVerifyRequest.class)
    @Anonymous
    @PostMapping("/idcard")
    public AjaxResult verifyIdCard(@RequestBody DriverIdCardVerifyRequest request) {
        VerifyResponseData responseData = driverVerifyService.verifyIdCard(request);
        return AjaxResult.success("请求处理成功", responseData);
    }
    
    /**
     * 驾驶证批量验证接口
     */
    @ApiOperation(value = "驾驶证批量验证", 
                  notes = "验证驾驶证信息的真实性和有效性。支持验证驾驶证号码、身份证号、姓名、驾照类型、档案编号等信息的一致性。\n\n" +
                         "请求示例：\n" +
                         "```json\n" +
                         "{\n" +
                         "  \"verifyType\": \"authenticity\",\n" +
                         "  \"licenses\": [\n" +
                         "    {\n" +
                         "      \"idCard\": \"6105281122111138\",\n" +
                         "      \"name\": \"李四\",\n" +
                         "      \"licenseNumber\": \"610528199909191102\",\n" +
                         "      \"licenseType\": \"B2\",\n" +
                         "      \"archiveNumber\": \"610528001234\"\n" +
                         "    },\n" +
                         "    {\n" +
                         "      \"idCard\": \"6105291122222222\",\n" +
                         "      \"name\": \"张三\",\n" +
                         "      \"licenseNumber\": \"610529199909222222\",\n" +
                         "      \"licenseType\": \"B2\",\n" +
                         "      \"archiveNumber\": \"610529001235\"\n" +
                         "    }\n" +
                         "  ]\n" +
                         "}\n" +
                         "```")
    @ApiImplicitParam(name = "request", value = "驾驶证验证请求参数", required = true, dataTypeClass = DriverLicenseVerifyRequest.class)
    @Anonymous
    @PostMapping("/drivinglicense")
    public AjaxResult verifyDriverLicense(@RequestBody DriverLicenseVerifyRequest request) {
        VerifyResponseData responseData = driverVerifyService.verifyDriverLicense(request);
        return AjaxResult.success("请求处理成功", responseData);
    }
    
    /**
     * 从业资格证批量验证接口
     */
    @ApiOperation(value = "从业资格证批量验证", 
                  notes = "验证从业资格证信息的真实性和有效性，包括证件编号、姓名、有效期等信息。\n\n" +
                         "请求示例：\n" +
                         "```json\n" +
                         "{\n" +
                         "  \"verifyType\": \"authenticity\",\n" +
                         "  \"qualifications\": [\n" +
                         "    {\n" +
                         "      \"name\": \"王五\",\n" +
                         "      \"qualificationNumber\": \"610528202001010001\",\n" +
                         "      \"validStartDate\": \"2020-01-01\",\n" +
                         "      \"validEndDate\": \"2026-01-01\"\n" +
                         "    }\n" +
                         "  ]\n" +
                         "}\n" +
                         "```")
    @ApiImplicitParam(name = "request", value = "从业资格证验证请求参数", required = true, dataTypeClass = DriverQualificationVerifyRequest.class)
    @Anonymous
    @PostMapping("/qualification")
    public AjaxResult verifyQualification(@RequestBody DriverQualificationVerifyRequest request) {
        VerifyResponseData responseData = driverVerifyService.verifyQualification(request);
        return AjaxResult.success("请求处理成功", responseData);
    }
} 