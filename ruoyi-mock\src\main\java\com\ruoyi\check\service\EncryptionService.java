package com.ruoyi.check.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.check.domain.EncryptedRequest;
import com.ruoyi.check.domain.EncryptedResponse;
import com.ruoyi.check.utils.CryptoUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.security.PublicKey;
import java.util.Base64;

/**
 * 加密解密服务
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class EncryptionService {
    
    private static final Logger logger = LoggerFactory.getLogger(EncryptionService.class);
    
    @Autowired
    private KeyManagementService keyManagementService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 防重放攻击的时间窗口（5分钟）
    private static final long REPLAY_ATTACK_WINDOW = 5 * 60 * 1000;
    
    /**
     * 解密请求数据
     */
    public <T> T decryptRequest(EncryptedRequest encryptedRequest, Class<T> targetClass) {
        try {
            // 1. 验证时间戳，防止重放攻击
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - encryptedRequest.getTimestamp()) > REPLAY_ATTACK_WINDOW) {
                throw new SecurityException("请求时间戳无效，可能存在重放攻击");
            }
            
            // 2. 使用服务端私钥解密AES密钥
            String aesKeyStr = CryptoUtils.rsaDecrypt(
                encryptedRequest.getEncryptedAESKey(), 
                keyManagementService.getServerPrivateKey()
            );
            SecretKey aesKey = CryptoUtils.stringToAESKey(aesKeyStr);
            
            // 3. 解密IV
            byte[] iv = Base64.getDecoder().decode(encryptedRequest.getIv());
            
            // 4. 使用AES密钥解密数据
            String decryptedData = CryptoUtils.aesDecrypt(
                encryptedRequest.getEncryptedData(), 
                aesKey, 
                iv
            );
            
            // 5. 验证数据完整性
            if (!CryptoUtils.verifyIntegrity(decryptedData, encryptedRequest.getDataHash())) {
                throw new SecurityException("数据完整性校验失败，数据可能被篡改");
            }
            
            // 6. 反序列化为目标对象
            T result = objectMapper.readValue(decryptedData, targetClass);
            
            logger.info("请求数据解密成功");
            return result;
            
        } catch (Exception e) {
            logger.error("解密请求数据失败", e);
            throw new RuntimeException("解密请求数据失败", e);
        }
    }
    
    /**
     * 加密响应数据
     */
    public EncryptedResponse encryptResponse(Object responseData, SecretKey aesKey) {
        try {
            // 1. 序列化响应数据
            String jsonData = objectMapper.writeValueAsString(responseData);
            
            // 2. 计算数据哈希值
            String dataHash = CryptoUtils.calculateSHA256(jsonData);
            
            // 3. 生成随机IV
            byte[] iv = CryptoUtils.generateIV();
            
            // 4. 使用AES密钥加密数据
            String encryptedData = CryptoUtils.aesEncrypt(jsonData, aesKey, iv);
            
            // 5. 创建加密响应
            EncryptedResponse encryptedResponse = new EncryptedResponse(
                Base64.getEncoder().encodeToString(iv),
                encryptedData,
                dataHash,
                System.currentTimeMillis()
            );
            
            logger.info("响应数据加密成功");
            return encryptedResponse;
            
        } catch (Exception e) {
            logger.error("加密响应数据失败", e);
            throw new RuntimeException("加密响应数据失败", e);
        }
    }
    
    /**
     * 从加密请求中提取AES密钥（用于响应加密）
     */
    public SecretKey extractAESKey(EncryptedRequest encryptedRequest) {
        try {
            String aesKeyStr = CryptoUtils.rsaDecrypt(
                encryptedRequest.getEncryptedAESKey(), 
                keyManagementService.getServerPrivateKey()
            );
            return CryptoUtils.stringToAESKey(aesKeyStr);
        } catch (Exception e) {
            logger.error("提取AES密钥失败", e);
            throw new RuntimeException("提取AES密钥失败", e);
        }
    }
    
    /**
     * 为客户端加密数据（用于测试）
     */
    public EncryptedRequest encryptForClient(Object requestData, String clientId) {
        try {
            // 1. 获取客户端公钥
            PublicKey clientPublicKey = keyManagementService.getClientPublicKey(clientId);
            if (clientPublicKey == null) {
                throw new IllegalArgumentException("客户端公钥不存在: " + clientId);
            }
            
            // 2. 生成AES密钥
            SecretKey aesKey = CryptoUtils.generateAESKey();
            
            // 3. 序列化请求数据
            String jsonData = objectMapper.writeValueAsString(requestData);
            
            // 4. 计算数据哈希值
            String dataHash = CryptoUtils.calculateSHA256(jsonData);
            
            // 5. 生成随机IV
            byte[] iv = CryptoUtils.generateIV();
            
            // 6. 使用AES密钥加密数据
            String encryptedData = CryptoUtils.aesEncrypt(jsonData, aesKey, iv);
            
            // 7. 使用客户端公钥加密AES密钥
            String encryptedAESKey = CryptoUtils.rsaEncrypt(
                CryptoUtils.aesKeyToString(aesKey), 
                clientPublicKey
            );
            
            // 8. 创建加密请求
            EncryptedRequest encryptedRequest = new EncryptedRequest(
                encryptedAESKey,
                Base64.getEncoder().encodeToString(iv),
                encryptedData,
                dataHash,
                System.currentTimeMillis()
            );
            
            logger.info("为客户端加密数据成功: {}", clientId);
            return encryptedRequest;
            
        } catch (Exception e) {
            logger.error("为客户端加密数据失败: {}", clientId, e);
            throw new RuntimeException("为客户端加密数据失败", e);
        }
    }
} 