package com.ruoyi.check.domain.dto;

import java.util.List;

/**
 * 从业资格证验证响应DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class QualificationVerifyResponse {
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应代码
     */
    private Integer code;
    
    /**
     * 响应数据
     */
    private ResponseData data;
    
    /**
     * 响应数据
     */
    public static class ResponseData {
        /**
         * 验证类型
         */
        private String verifyType;
        
        /**
         * 总数
         */
        private Integer total;
        
        /**
         * 验证结果列表
         */
        private List<QualificationResult> results;
        
        // Getter和Setter方法
        public String getVerifyType() {
            return verifyType;
        }
        
        public void setVerifyType(String verifyType) {
            this.verifyType = verifyType;
        }
        
        public Integer getTotal() {
            return total;
        }
        
        public void setTotal(Integer total) {
            this.total = total;
        }
        
        public List<QualificationResult> getResults() {
            return results;
        }
        
        public void setResults(List<QualificationResult> results) {
            this.results = results;
        }
    }
    
    /**
     * 从业资格证验证结果
     */
    public static class QualificationResult {
        /**
         * 手机号（响应中为null）
         */
        private String phone;
        
        /**
         * 身份证号（响应中为null）
         */
        private String idCard;
        
        /**
         * 姓名
         */
        private String name;
        
        /**
         * 驾驶证号（响应中为null）
         */
        private String licenseNumber;
        
        /**
         * 从业资格证编号
         */
        private String qualificationNumber;
        
        /**
         * 车牌号（响应中为null）
         */
        private String plateNumber;
        
        /**
         * 道路运输证号（响应中为null）
         */
        private String transportLicenseNumber;
        
        /**
         * 所有人（响应中为null）
         */
        private String owner;
        
        /**
         * 格式是否有效（响应中为null）
         */
        private String formatValid;
        
        /**
         * 验证结果
         */
        private String verifyResult;
        
        /**
         * 验证消息
         */
        private String message;
        
        /**
         * 正确信息（响应中为null）
         */
        private String correctInfo;
        
        // Getter和Setter方法
        public String getPhone() {
            return phone;
        }
        
        public void setPhone(String phone) {
            this.phone = phone;
        }
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getLicenseNumber() {
            return licenseNumber;
        }
        
        public void setLicenseNumber(String licenseNumber) {
            this.licenseNumber = licenseNumber;
        }
        
        public String getQualificationNumber() {
            return qualificationNumber;
        }
        
        public void setQualificationNumber(String qualificationNumber) {
            this.qualificationNumber = qualificationNumber;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getTransportLicenseNumber() {
            return transportLicenseNumber;
        }
        
        public void setTransportLicenseNumber(String transportLicenseNumber) {
            this.transportLicenseNumber = transportLicenseNumber;
        }
        
        public String getOwner() {
            return owner;
        }
        
        public void setOwner(String owner) {
            this.owner = owner;
        }
        
        public String getFormatValid() {
            return formatValid;
        }
        
        public void setFormatValid(String formatValid) {
            this.formatValid = formatValid;
        }
        
        public String getVerifyResult() {
            return verifyResult;
        }
        
        public void setVerifyResult(String verifyResult) {
            this.verifyResult = verifyResult;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public String getCorrectInfo() {
            return correctInfo;
        }
        
        public void setCorrectInfo(String correctInfo) {
            this.correctInfo = correctInfo;
        }
    }
    
    // Getter和Setter方法
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public ResponseData getData() {
        return data;
    }
    
    public void setData(ResponseData data) {
        this.data = data;
    }
} 