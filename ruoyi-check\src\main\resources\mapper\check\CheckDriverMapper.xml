<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.check.mapper.CheckDriverMapper">
    
    <resultMap type="CheckDriver" id="CheckDriverResult">
        <result property="driverId"    column="driver_id"    />
        <result property="mdDriverId"    column="md_driver_id"    />
        <result property="carrierId"    column="CARRIER_ID"    />
        <result property="loginName"    column="LOGIN_NAME"    />
        <result property="NAME"    column="NAME"    />
        <result property="TELEPHONE"    column="TELEPHONE"    />
        <result property="idNo"    column="ID_NO"    />
        <result property="idValidFrom"    column="ID_VALID_FROM"    />
        <result property="idValidTo"    column="ID_VALID_TO"    />
        <result property="idLongTerm"    column="ID_LONG_TERM"    />
        <result property="licenseNo"    column="LICENSE_NO"    />
        <result property="licenseClass"    column="LICENSE_CLASS"    />
        <result property="licenseValidFrom"    column="LICENSE_VALID_FROM"    />
        <result property="licenseValidTo"    column="LICENSE_VALID_TO"    />
        <result property="licenseLongTerm"    column="LICENSE_LONG_TERM"    />
        <result property="CERTIFICATE"    column="CERTIFICATE"    />
        <result property="certificateIssueOrg"    column="CERTIFICATE_ISSUE_ORG"    />
        <result property="certificateFrom"    column="CERTIFICATE_FROM"    />
        <result property="certificateTo"    column="CERTIFICATE_TO"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateId"    column="UPDATE_ID"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="licenseFileNo"    column="LICENSE_FILE_NO"    />
        <result property="checkCreateTime"    column="CHECK_CREATE_TIME"    />
        <result property="checkStatus"    column="CHECK_STATUS"    />
    </resultMap>

    <sql id="selectCheckDriverVo">
        select driver_id, md_driver_id, CARRIER_ID, LOGIN_NAME, NAME, TELEPHONE, ID_NO, ID_VALID_FROM, ID_VALID_TO, ID_LONG_TERM, LICENSE_NO, LICENSE_CLASS, LICENSE_VALID_FROM, LICENSE_VALID_TO, LICENSE_LONG_TERM, CERTIFICATE, CERTIFICATE_ISSUE_ORG, CERTIFICATE_FROM, CERTIFICATE_TO, CREATE_BY, CREATE_TIME, UPDATE_ID, UPDATE_BY, UPDATE_TIME, LICENSE_FILE_NO, CHECK_CREATE_TIME, CHECK_STATUS from check_driver
    </sql>

    <select id="selectCheckDriverList" parameterType="CheckDriver" resultMap="CheckDriverResult">
        <include refid="selectCheckDriverVo"/>
        <where>  
            <if test="mdDriverId != null "> and md_driver_id = #{mdDriverId}</if>
            <if test="carrierId != null "> and CARRIER_ID = #{carrierId}</if>
            <if test="loginName != null  and loginName != ''"> and LOGIN_NAME like concat('%', #{loginName}, '%')</if>
            <if test="NAME != null  and NAME != ''"> and NAME like concat('%', #{NAME}, '%')</if>
            <if test="TELEPHONE != null  and TELEPHONE != ''"> and TELEPHONE = #{TELEPHONE}</if>
            <if test="idNo != null  and idNo != ''"> and ID_NO = #{idNo}</if>
            <if test="idValidFrom != null "> and ID_VALID_FROM = #{idValidFrom}</if>
            <if test="idValidTo != null "> and ID_VALID_TO = #{idValidTo}</if>
            <if test="idLongTerm != null "> and ID_LONG_TERM = #{idLongTerm}</if>
            <if test="licenseNo != null  and licenseNo != ''"> and LICENSE_NO = #{licenseNo}</if>
            <if test="licenseClass != null  and licenseClass != ''"> and LICENSE_CLASS = #{licenseClass}</if>
            <if test="licenseValidFrom != null "> and LICENSE_VALID_FROM = #{licenseValidFrom}</if>
            <if test="licenseValidTo != null "> and LICENSE_VALID_TO = #{licenseValidTo}</if>
            <if test="licenseLongTerm != null "> and LICENSE_LONG_TERM = #{licenseLongTerm}</if>
            <if test="CERTIFICATE != null  and CERTIFICATE != ''"> and CERTIFICATE = #{CERTIFICATE}</if>
            <if test="certificateIssueOrg != null  and certificateIssueOrg != ''"> and CERTIFICATE_ISSUE_ORG = #{certificateIssueOrg}</if>
            <if test="certificateFrom != null "> and CERTIFICATE_FROM = #{certificateFrom}</if>
            <if test="certificateTo != null "> and CERTIFICATE_TO = #{certificateTo}</if>
            <if test="createBy != null  and createBy != ''"> and CREATE_BY = #{createBy}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updateId != null "> and UPDATE_ID = #{updateId}</if>
            <if test="updateBy != null  and updateBy != ''"> and UPDATE_BY = #{updateBy}</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="licenseFileNo != null  and licenseFileNo != ''"> and LICENSE_FILE_NO = #{licenseFileNo}</if>
            <if test="checkCreateTime != null "> and CHECK_CREATE_TIME = #{checkCreateTime}</if>
            <if test="checkStatus != null "> and CHECK_STATUS = #{checkStatus}</if>
        </where>
    </select>
    
    <select id="selectCheckDriverByDriverId" parameterType="Long" resultMap="CheckDriverResult">
        <include refid="selectCheckDriverVo"/>
        where driver_id = #{driverId}
    </select>

    <insert id="insertCheckDriver" parameterType="CheckDriver" useGeneratedKeys="true" keyProperty="driverId">
        insert into check_driver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mdDriverId != null">md_driver_id,</if>
            <if test="carrierId != null">CARRIER_ID,</if>
            <if test="loginName != null">LOGIN_NAME,</if>
            <if test="NAME != null">NAME,</if>
            <if test="TELEPHONE != null and TELEPHONE != ''">TELEPHONE,</if>
            <if test="idNo != null">ID_NO,</if>
            <if test="idValidFrom != null">ID_VALID_FROM,</if>
            <if test="idValidTo != null">ID_VALID_TO,</if>
            <if test="idLongTerm != null">ID_LONG_TERM,</if>
            <if test="licenseNo != null">LICENSE_NO,</if>
            <if test="licenseClass != null">LICENSE_CLASS,</if>
            <if test="licenseValidFrom != null">LICENSE_VALID_FROM,</if>
            <if test="licenseValidTo != null">LICENSE_VALID_TO,</if>
            <if test="licenseLongTerm != null">LICENSE_LONG_TERM,</if>
            <if test="CERTIFICATE != null">CERTIFICATE,</if>
            <if test="certificateIssueOrg != null">CERTIFICATE_ISSUE_ORG,</if>
            <if test="certificateFrom != null">CERTIFICATE_FROM,</if>
            <if test="certificateTo != null">CERTIFICATE_TO,</if>
            <if test="createBy != null and createBy != ''">CREATE_BY,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateId != null">UPDATE_ID,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="licenseFileNo != null">LICENSE_FILE_NO,</if>
            <if test="checkCreateTime != null">CHECK_CREATE_TIME,</if>
            <if test="checkStatus != null">CHECK_STATUS,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mdDriverId != null">#{mdDriverId},</if>
            <if test="carrierId != null">#{carrierId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="NAME != null">#{NAME},</if>
            <if test="TELEPHONE != null and TELEPHONE != ''">#{TELEPHONE},</if>
            <if test="idNo != null">#{idNo},</if>
            <if test="idValidFrom != null">#{idValidFrom},</if>
            <if test="idValidTo != null">#{idValidTo},</if>
            <if test="idLongTerm != null">#{idLongTerm},</if>
            <if test="licenseNo != null">#{licenseNo},</if>
            <if test="licenseClass != null">#{licenseClass},</if>
            <if test="licenseValidFrom != null">#{licenseValidFrom},</if>
            <if test="licenseValidTo != null">#{licenseValidTo},</if>
            <if test="licenseLongTerm != null">#{licenseLongTerm},</if>
            <if test="CERTIFICATE != null">#{CERTIFICATE},</if>
            <if test="certificateIssueOrg != null">#{certificateIssueOrg},</if>
            <if test="certificateFrom != null">#{certificateFrom},</if>
            <if test="certificateTo != null">#{certificateTo},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="licenseFileNo != null">#{licenseFileNo},</if>
            <if test="checkCreateTime != null">#{checkCreateTime},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
         </trim>
    </insert>

    <update id="updateCheckDriver" parameterType="CheckDriver">
        update check_driver
        <trim prefix="SET" suffixOverrides=",">
            <if test="mdDriverId != null">md_driver_id = #{mdDriverId},</if>
            <if test="carrierId != null">CARRIER_ID = #{carrierId},</if>
            <if test="loginName != null">LOGIN_NAME = #{loginName},</if>
            <if test="NAME != null">NAME = #{NAME},</if>
            <if test="TELEPHONE != null and TELEPHONE != ''">TELEPHONE = #{TELEPHONE},</if>
            <if test="idNo != null">ID_NO = #{idNo},</if>
            <if test="idValidFrom != null">ID_VALID_FROM = #{idValidFrom},</if>
            <if test="idValidTo != null">ID_VALID_TO = #{idValidTo},</if>
            <if test="idLongTerm != null">ID_LONG_TERM = #{idLongTerm},</if>
            <if test="licenseNo != null">LICENSE_NO = #{licenseNo},</if>
            <if test="licenseClass != null">LICENSE_CLASS = #{licenseClass},</if>
            <if test="licenseValidFrom != null">LICENSE_VALID_FROM = #{licenseValidFrom},</if>
            <if test="licenseValidTo != null">LICENSE_VALID_TO = #{licenseValidTo},</if>
            <if test="licenseLongTerm != null">LICENSE_LONG_TERM = #{licenseLongTerm},</if>
            <if test="CERTIFICATE != null">CERTIFICATE = #{CERTIFICATE},</if>
            <if test="certificateIssueOrg != null">CERTIFICATE_ISSUE_ORG = #{certificateIssueOrg},</if>
            <if test="certificateFrom != null">CERTIFICATE_FROM = #{certificateFrom},</if>
            <if test="certificateTo != null">CERTIFICATE_TO = #{certificateTo},</if>
            <if test="createBy != null and createBy != ''">CREATE_BY = #{createBy},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateId != null">UPDATE_ID = #{updateId},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="licenseFileNo != null">LICENSE_FILE_NO = #{licenseFileNo},</if>
            <if test="checkCreateTime != null">CHECK_CREATE_TIME = #{checkCreateTime},</if>
            <if test="checkStatus != null">CHECK_STATUS = #{checkStatus},</if>
        </trim>
        where driver_id = #{driverId}
    </update>

    <delete id="deleteCheckDriverByDriverId" parameterType="Long">
        delete from check_driver where driver_id = #{driverId}
    </delete>

    <delete id="deleteCheckDriverByDriverIds" parameterType="String">
        delete from check_driver where driver_id in 
        <foreach item="driverId" collection="array" open="(" separator="," close=")">
            #{driverId}
        </foreach>
    </delete>
    
    <!-- 分页查询司机列表（用于批量处理） -->
    <select id="selectCheckDriverListByPage" resultMap="CheckDriverResult">
        <include refid="selectCheckDriverVo"/>
        order by driver_id
        limit #{offset}, #{limit}
    </select>
    
    <!-- 统计司机总数 -->
    <select id="countCheckDrivers" resultType="long">
        select count(1) from check_driver
    </select>
</mapper>