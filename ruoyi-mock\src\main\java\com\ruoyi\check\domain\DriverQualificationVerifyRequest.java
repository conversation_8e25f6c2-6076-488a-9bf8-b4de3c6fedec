package com.ruoyi.check.domain;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 司机从业资格证验证请求对象
 */
@ApiModel(value = "DriverQualificationVerifyRequest", description = "司机从业资格证验证请求")
public class DriverQualificationVerifyRequest {
    
    @ApiModelProperty(value = "验证类型：authenticity（真实性）| validity（有效性）", required = true, example = "authenticity")
    private String verifyType;  // 验证类型：authenticity（真实性）| validity（有效性）
    
    @ApiModelProperty(value = "从业资格证验证数据列表", required = true)
    private List<DriverQualificationData> qualifications;
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<DriverQualificationData> getQualifications() {
        return qualifications;
    }
    
    public void setQualifications(List<DriverQualificationData> qualifications) {
        this.qualifications = qualifications;
    }
    
    @ApiModel(value = "DriverQualificationData", description = "司机从业资格证验证数据")
    public static class DriverQualificationData {
        @ApiModelProperty(value = "姓名（明文）", required = true, example = "王五")
        private String name;                  // 姓名（明文）
        
        @ApiModelProperty(value = "从业资格证编号", required = true, example = "610528202001010001")
        private String qualificationNumber;   // 从业资格证编号
        
        @ApiModelProperty(value = "有效期起始日期", required = true, example = "2020-01-01")
        private String validStartDate;        // 有效期起始日期
        
        @ApiModelProperty(value = "有效期结束日期", required = true, example = "2026-01-01")
        private String validEndDate;          // 有效期结束日期
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getQualificationNumber() {
            return qualificationNumber;
        }
        
        public void setQualificationNumber(String qualificationNumber) {
            this.qualificationNumber = qualificationNumber;
        }
        
        public String getValidStartDate() {
            return validStartDate;
        }
        
        public void setValidStartDate(String validStartDate) {
            this.validStartDate = validStartDate;
        }
        
        public String getValidEndDate() {
            return validEndDate;
        }
        
        public void setValidEndDate(String validEndDate) {
            this.validEndDate = validEndDate;
        }
    }
} 