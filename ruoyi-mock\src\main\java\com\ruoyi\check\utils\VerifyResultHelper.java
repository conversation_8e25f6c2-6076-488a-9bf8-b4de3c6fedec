package com.ruoyi.check.utils;

import com.ruoyi.check.domain.VerifyResponseData;

/**
 * 验证结果辅助工具类
 */
public class VerifyResultHelper {
    
    /**
     * 根据验证结果设置结果代码
     * @param result 验证结果对象
     * @param verifyResult 验证结果字符串
     */
    public static void setResultCode(VerifyResponseData.VerifyResult result, String verifyResult) {
        result.setVerifyResult(verifyResult);
        
        // 根据验证结果设置结果代码
        switch (verifyResult) {
            case "一致":
                result.setResultCode(0);
                break;
            case "不一致":
                result.setResultCode(1);
                break;
            case "失效":
                result.setResultCode(2);
                break;
            case "不存在":
                result.setResultCode(3);
                break;
            default:
                result.setResultCode(0); // 默认为一致
                break;
        }
    }
} 