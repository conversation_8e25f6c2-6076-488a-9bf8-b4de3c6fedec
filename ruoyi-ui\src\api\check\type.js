import request from '@/utils/request'

// 查询验证列表
export function listType(query) {
  return request({
    url: '/check/type/list',
    method: 'get',
    params: query
  })
}

// 查询验证详细
export function getType(typeId) {
  return request({
    url: '/check/type/' + typeId,
    method: 'get'
  })
}

// 新增验证
export function addType(data) {
  return request({
    url: '/check/type',
    method: 'post',
    data: data
  })
}

// 修改验证
export function updateType(data) {
  return request({
    url: '/check/type',
    method: 'put',
    data: data
  })
}

// 删除验证
export function delType(typeId) {
  return request({
    url: '/check/type/' + typeId,
    method: 'delete'
  })
}
