package com.ruoyi.check.controller;

import java.util.List;

/**
 * 车辆轨迹查询请求体
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class VehicleTrackQueryRequest {
    
    /** 车辆信息列表 */
    private List<VehicleInfo> vehicles;
    
    public VehicleTrackQueryRequest() {
    }
    
    public VehicleTrackQueryRequest(List<VehicleInfo> vehicles) {
        this.vehicles = vehicles;
    }
    
    public List<VehicleInfo> getVehicles() {
        return vehicles;
    }
    
    public void setVehicles(List<VehicleInfo> vehicles) {
        this.vehicles = vehicles;
    }
    
    @Override
    public String toString() {
        return "VehicleTrackQueryRequest{" +
                "vehicles=" + vehicles +
                '}';
    }
    
    /**
     * 车辆信息内部类
     */
    public static class VehicleInfo {
        /** 车牌号 */
        private String plateNumber;
        
        /** 装车时间 */
        private String loadTime;
        
        /** 运抵时间 */
        private String arriveTime;
        
        public VehicleInfo() {
        }
        
        public VehicleInfo(String plateNumber, String loadTime, String arriveTime) {
            this.plateNumber = plateNumber;
            this.loadTime = loadTime;
            this.arriveTime = arriveTime;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getLoadTime() {
            return loadTime;
        }
        
        public void setLoadTime(String loadTime) {
            this.loadTime = loadTime;
        }
        
        public String getArriveTime() {
            return arriveTime;
        }
        
        public void setArriveTime(String arriveTime) {
            this.arriveTime = arriveTime;
        }
        
        @Override
        public String toString() {
            return "VehicleInfo{" +
                    "plateNumber='" + plateNumber + '\'' +
                    ", loadTime='" + loadTime + '\'' +
                    ", arriveTime='" + arriveTime + '\'' +
                    '}';
        }
    }
} 