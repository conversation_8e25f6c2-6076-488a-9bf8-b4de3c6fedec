<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="md_vehicle 表主键" prop="mdVehicleId">
        <el-input
          v-model="queryParams.mdVehicleId"
          placeholder="请输入md_vehicle 表主键"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNo">
        <el-input
          v-model="queryParams.plateNo"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆识别代码" prop="CODE">
        <el-input
          v-model="queryParams.CODE"
          placeholder="请输入车辆识别代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认司机" prop="driverId">
        <el-input
          v-model="queryParams.driverId"
          placeholder="请输入默认司机"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车型ID" prop="vehicleTypeId">
        <el-input
          v-model="queryParams.vehicleTypeId"
          placeholder="请输入车型ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行驶证编码" prop="licenseNo">
        <el-input
          v-model="queryParams.licenseNo"
          placeholder="请输入行驶证编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行驶证注册日期" prop="registerDate">
        <el-date-picker clearable
          v-model="queryParams.registerDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择行驶证注册日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="行驶证发证时间" prop="issueDate">
        <el-date-picker clearable
          v-model="queryParams.issueDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择行驶证发证时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="行驶证到期日" prop="issueExpiryDate">
        <el-date-picker clearable
          v-model="queryParams.issueExpiryDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择行驶证到期日">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="车主姓名" prop="OWNER">
        <el-input
          v-model="queryParams.OWNER"
          placeholder="请输入车主姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="道路运输许可证号" prop="roadCard">
        <el-input
          v-model="queryParams.roadCard"
          placeholder="请输入道路运输许可证号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="运输证到期日" prop="roadCardExpiryDate">
        <el-date-picker clearable
          v-model="queryParams.roadCardExpiryDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择运输证到期日">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="承运商ID" prop="carrierId">
        <el-input
          v-model="queryParams.carrierId"
          placeholder="请输入承运商ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车主身份证" prop="ownerIdcard">
        <el-input
          v-model="queryParams.ownerIdcard"
          placeholder="请输入车主身份证"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车主手机" prop="ownerPhone">
        <el-input
          v-model="queryParams.ownerPhone"
          placeholder="请输入车主手机"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人id" prop="createId">
        <el-input
          v-model="queryParams.createId"
          placeholder="请输入创建人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="修改人id" prop="updateId">
        <el-input
          v-model="queryParams.updateId"
          placeholder="请输入修改人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改人" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入修改人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间" prop="updateTime">
        <el-date-picker clearable
          v-model="queryParams.updateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择修改时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="行驶证验证信息" prop="issueVerifyMsg">
        <el-input
          v-model="queryParams.issueVerifyMsg"
          placeholder="请输入行驶证验证信息"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="道路运输证验证信息" prop="roadCardVerifyMsg">
        <el-input
          v-model="queryParams.roadCardVerifyMsg"
          placeholder="请输入道路运输证验证信息"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆类型" prop="vclTpNm">
        <el-input
          v-model="queryParams.vclTpNm"
          placeholder="请输入车辆类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="验证创建时间" prop="checkCreateTime">
        <el-date-picker clearable
          v-model="queryParams.checkCreateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择验证创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['check:vehicle:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['check:vehicle:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['check:vehicle:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['check:vehicle:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="vehicleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="车辆主键" align="center" prop="vehicleId" />
      <el-table-column label="md_vehicle 表主键" align="center" prop="mdVehicleId" />
      <el-table-column label="车牌号" align="center" prop="plateNo" />
      <el-table-column label="车辆识别代码" align="center" prop="CODE" />
      <el-table-column label="默认司机" align="center" prop="driverId" />
      <el-table-column label="车型ID" align="center" prop="vehicleTypeId" />
      <el-table-column label="车型" align="center" prop="vehicleType" />
      <el-table-column label="行驶证编码" align="center" prop="licenseNo" />
      <el-table-column label="行驶证注册日期" align="center" prop="registerDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.registerDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="行驶证发证时间" align="center" prop="issueDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.issueDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="行驶证到期日" align="center" prop="issueExpiryDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.issueExpiryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车主姓名" align="center" prop="OWNER" />
      <el-table-column label="道路运输许可证号" align="center" prop="roadCard" />
      <el-table-column label="运输证到期日" align="center" prop="roadCardExpiryDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.roadCardExpiryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="承运商ID" align="center" prop="carrierId" />
      <el-table-column label="车主身份证" align="center" prop="ownerIdcard" />
      <el-table-column label="车主手机" align="center" prop="ownerPhone" />
      <el-table-column label="创建人id" align="center" prop="createId" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改人id" align="center" prop="updateId" />
      <el-table-column label="修改人" align="center" prop="updateBy" />
      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="行驶证验证状态(0-待验证、1-验证失败、2-验证不通过、3-验证通过)" align="center" prop="issueVerifyStatus" />
      <el-table-column label="行驶证验证信息" align="center" prop="issueVerifyMsg" />
      <el-table-column label=" 道路运输证验证状态(0-待验证、1-验证失败、2-验证不通过、3-验证通过)" align="center" prop="roadCardVerifyStatus" />
      <el-table-column label="道路运输证验证信息" align="center" prop="roadCardVerifyMsg" />
      <el-table-column label="车辆类型" align="center" prop="vclTpNm" />
      <el-table-column label="验证创建时间" align="center" prop="checkCreateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.checkCreateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="执行标识。0：未执行，1：已执行" align="center" prop="checkStatus" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['check:vehicle:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['check:vehicle:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改车辆信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="md_vehicle 表主键" prop="mdVehicleId">
          <el-input v-model="form.mdVehicleId" placeholder="请输入md_vehicle 表主键" />
        </el-form-item>
        <el-form-item label="车牌号" prop="plateNo">
          <el-input v-model="form.plateNo" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="车辆识别代码" prop="CODE">
          <el-input v-model="form.CODE" placeholder="请输入车辆识别代码" />
        </el-form-item>
        <el-form-item label="默认司机" prop="driverId">
          <el-input v-model="form.driverId" placeholder="请输入默认司机" />
        </el-form-item>
        <el-form-item label="车型ID" prop="vehicleTypeId">
          <el-input v-model="form.vehicleTypeId" placeholder="请输入车型ID" />
        </el-form-item>
        <el-form-item label="行驶证编码" prop="licenseNo">
          <el-input v-model="form.licenseNo" placeholder="请输入行驶证编码" />
        </el-form-item>
        <el-form-item label="行驶证注册日期" prop="registerDate">
          <el-date-picker clearable
            v-model="form.registerDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择行驶证注册日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="行驶证发证时间" prop="issueDate">
          <el-date-picker clearable
            v-model="form.issueDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择行驶证发证时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="行驶证到期日" prop="issueExpiryDate">
          <el-date-picker clearable
            v-model="form.issueExpiryDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择行驶证到期日">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="车主姓名" prop="OWNER">
          <el-input v-model="form.OWNER" placeholder="请输入车主姓名" />
        </el-form-item>
        <el-form-item label="道路运输许可证号" prop="roadCard">
          <el-input v-model="form.roadCard" placeholder="请输入道路运输许可证号" />
        </el-form-item>
        <el-form-item label="运输证到期日" prop="roadCardExpiryDate">
          <el-date-picker clearable
            v-model="form.roadCardExpiryDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择运输证到期日">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="承运商ID" prop="carrierId">
          <el-input v-model="form.carrierId" placeholder="请输入承运商ID" />
        </el-form-item>
        <el-form-item label="车主身份证" prop="ownerIdcard">
          <el-input v-model="form.ownerIdcard" placeholder="请输入车主身份证" />
        </el-form-item>
        <el-form-item label="车主手机" prop="ownerPhone">
          <el-input v-model="form.ownerPhone" placeholder="请输入车主手机" />
        </el-form-item>
        <el-form-item label="创建人id" prop="createId">
          <el-input v-model="form.createId" placeholder="请输入创建人id" />
        </el-form-item>
        <el-form-item label="修改人id" prop="updateId">
          <el-input v-model="form.updateId" placeholder="请输入修改人id" />
        </el-form-item>
        <el-form-item label="行驶证验证信息" prop="issueVerifyMsg">
          <el-input v-model="form.issueVerifyMsg" placeholder="请输入行驶证验证信息" />
        </el-form-item>
        <el-form-item label="道路运输证验证信息" prop="roadCardVerifyMsg">
          <el-input v-model="form.roadCardVerifyMsg" placeholder="请输入道路运输证验证信息" />
        </el-form-item>
        <el-form-item label="车辆类型" prop="vclTpNm">
          <el-input v-model="form.vclTpNm" placeholder="请输入车辆类型" />
        </el-form-item>
        <el-form-item label="验证创建时间" prop="checkCreateTime">
          <el-date-picker clearable
            v-model="form.checkCreateTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择验证创建时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVehicle, getVehicle, delVehicle, addVehicle, updateVehicle } from "@/api/check/vehicle"

export default {
  name: "Vehicle",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 车辆信息表格数据
      vehicleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mdVehicleId: null,
        plateNo: null,
        CODE: null,
        driverId: null,
        vehicleTypeId: null,
        vehicleType: null,
        licenseNo: null,
        registerDate: null,
        issueDate: null,
        issueExpiryDate: null,
        OWNER: null,
        roadCard: null,
        roadCardExpiryDate: null,
        carrierId: null,
        ownerIdcard: null,
        ownerPhone: null,
        createId: null,
        createBy: null,
        createTime: null,
        updateId: null,
        updateBy: null,
        updateTime: null,
        issueVerifyStatus: null,
        issueVerifyMsg: null,
        roadCardVerifyStatus: null,
        roadCardVerifyMsg: null,
        vclTpNm: null,
        checkCreateTime: null,
        checkStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        carrierId: [
          { required: true, message: "承运商ID不能为空", trigger: "blur" }
        ],
        createId: [
          { required: true, message: "创建人id不能为空", trigger: "blur" }
        ],
        issueVerifyStatus: [
          { required: true, message: "行驶证验证状态(0-待验证、1-验证失败、2-验证不通过、3-验证通过)不能为空", trigger: "change" }
        ],
        roadCardVerifyStatus: [
          { required: true, message: " 道路运输证验证状态(0-待验证、1-验证失败、2-验证不通过、3-验证通过)不能为空", trigger: "change" }
        ],
        checkCreateTime: [
          { required: true, message: "验证创建时间不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询车辆信息列表 */
    getList() {
      this.loading = true
      listVehicle(this.queryParams).then(response => {
        this.vehicleList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        vehicleId: null,
        mdVehicleId: null,
        plateNo: null,
        CODE: null,
        driverId: null,
        vehicleTypeId: null,
        vehicleType: null,
        licenseNo: null,
        registerDate: null,
        issueDate: null,
        issueExpiryDate: null,
        OWNER: null,
        roadCard: null,
        roadCardExpiryDate: null,
        carrierId: null,
        ownerIdcard: null,
        ownerPhone: null,
        createId: null,
        createBy: null,
        createTime: null,
        updateId: null,
        updateBy: null,
        updateTime: null,
        issueVerifyStatus: null,
        issueVerifyMsg: null,
        roadCardVerifyStatus: null,
        roadCardVerifyMsg: null,
        vclTpNm: null,
        checkCreateTime: null,
        checkStatus: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.vehicleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加车辆信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const vehicleId = row.vehicleId || this.ids
      getVehicle(vehicleId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改车辆信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.vehicleId != null) {
            updateVehicle(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addVehicle(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const vehicleIds = row.vehicleId || this.ids
      this.$modal.confirm('是否确认删除车辆信息编号为"' + vehicleIds + '"的数据项？').then(function() {
        return delVehicle(vehicleIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('check/vehicle/export', {
        ...this.queryParams
      }, `vehicle_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
