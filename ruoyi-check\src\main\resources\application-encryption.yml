# 加密传输配置
driver:
  verify:
    # 是否启用加密传输
    encryption:
      enabled: true
    
    # 明文接口URL
    phone:
      url: http://localhost:8080/api/v1/huoda/driver/verify/phone
    idcard:
      url: http://localhost:8080/api/v1/huoda/driver/verify/idcard
    license:
      url: http://localhost:8080/api/v1/huoda/driver/verify/drivinglicense
    qualification:
      url: http://localhost:8080/api/v1/huoda/driver/verify/qualification
    
    # 加密接口URL
    secure:
      phone:
        url: http://localhost:8080/api/v1/huoda/secure/driver/verify/phone
      idcard:
        url: http://localhost:8080/api/v1/huoda/secure/driver/verify/idcard
      license:
        url: http://localhost:8080/api/v1/huoda/secure/driver/verify/drivinglicense
      qualification:
        url: http://localhost:8080/api/v1/huoda/secure/driver/verify/qualification

vehicle:
  verify:
    # 是否启用加密传输
    encryption:
      enabled: true
    
    # 明文接口URL
    drivinglicense:
      url: http://localhost:8080/api/v1/huoda/vehicle/verify/drivinglicense
    roadtransport:
      url: http://localhost:8080/api/v1/huoda/vehicle/verify/roadtransport
    
    # 加密接口URL
    secure:
      drivinglicense:
        url: http://localhost:8080/api/v1/huoda/secure/vehicle/verify/drivinglicense
      roadtransport:
        url: http://localhost:8080/api/v1/huoda/secure/vehicle/verify/roadtransport

# Mock服务器配置
mock:
  server:
    public:
      key:
        url: http://localhost:8080/api/v1/huoda/secure/driver/verify/public-key 