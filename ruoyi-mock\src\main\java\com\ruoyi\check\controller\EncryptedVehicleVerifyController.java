package com.ruoyi.check.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.check.domain.EncryptedRequestWrapper;
import com.ruoyi.check.domain.*;
import com.ruoyi.check.service.VehicleVerifyService;
import com.ruoyi.check.utils.AESUtils;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 加密车辆验证控制器
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Api(tags = "加密车辆验证接口")
@RestController
@RequestMapping("/api/v1/huoda/encrypted/vehicle/verify")
public class EncryptedVehicleVerifyController {
    
    private static final Logger logger = LoggerFactory.getLogger(EncryptedVehicleVerifyController.class);
    
    @Autowired
    private VehicleVerifyService vehicleVerifyService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 加密车辆行驶证验证接口
     */
    @Anonymous
    @ApiOperation(value = "加密车辆行驶证验证", notes = "验证车辆行驶证信息（加密传输）")
    @PostMapping("/drivinglicense")
    public AjaxResult verifyVehicleDrivingLicenseEncrypted(@RequestBody EncryptedRequestWrapper encryptedRequest) {
        try {
            logger.info("接收到加密车辆行驶证验证请求");
            
            // 1. 解密请求数据
            String decryptedData = AESUtils.decrypt(encryptedRequest.getEncryptedData());
            logger.info("解密后的请求数据: {}", decryptedData);
            
            // 2. 反序列化为请求对象
            VehicleDrivingLicenseVerifyRequest request = objectMapper.readValue(decryptedData, VehicleDrivingLicenseVerifyRequest.class);
            
            // 3. 调用业务服务
            VerifyResponseData response = vehicleVerifyService.verifyDrivingLicense(request);
            
            // 4. 序列化响应数据
            String responseJson = objectMapper.writeValueAsString(response);
            
            // 5. 加密响应数据
            String encryptedResponse = AESUtils.encrypt(responseJson);
            
            // 6. 构建加密响应
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedResponse);
            result.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("请求处理成功", result);
            
        } catch (Exception e) {
            logger.error("加密车辆行驶证验证失败", e);
            return AjaxResult.error("加密车辆行驶证验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 加密车辆道路运输证验证接口
     */
    @Anonymous
    @ApiOperation(value = "加密车辆道路运输证验证", notes = "验证车辆道路运输证信息（加密传输）")
    @PostMapping("/roadtransport")
    public AjaxResult verifyVehicleRoadTransportEncrypted(@RequestBody EncryptedRequestWrapper encryptedRequest) {
        try {
            logger.info("接收到加密车辆道路运输证验证请求");
            
            // 1. 解密请求数据
            String decryptedData = AESUtils.decrypt(encryptedRequest.getEncryptedData());
            logger.info("解密后的请求数据: {}", decryptedData);
            
            // 2. 反序列化为请求对象
            VehicleRoadTransportVerifyRequest request = objectMapper.readValue(decryptedData, VehicleRoadTransportVerifyRequest.class);
            
            // 3. 调用业务服务
            VerifyResponseData response = vehicleVerifyService.verifyRoadTransport(request);
            
            // 4. 序列化响应数据
            String responseJson = objectMapper.writeValueAsString(response);
            
            // 5. 加密响应数据
            String encryptedResponse = AESUtils.encrypt(responseJson);
            
            // 6. 构建加密响应
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedResponse);
            result.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("请求处理成功", result);
            
        } catch (Exception e) {
            logger.error("加密车辆道路运输证验证失败", e);
            return AjaxResult.error("加密车辆道路运输证验证失败: " + e.getMessage());
        }
    }
} 