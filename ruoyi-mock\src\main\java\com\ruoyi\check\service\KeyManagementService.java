package com.ruoyi.check.service;

import com.ruoyi.check.utils.CryptoUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 密钥管理服务
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class KeyManagementService {
    
    private static final Logger logger = LoggerFactory.getLogger(KeyManagementService.class);
    
    // 服务端RSA密钥对
    private KeyPair serverKeyPair;
    
    // 客户端公钥缓存（实际项目中应该从数据库或配置中心获取）
    private final ConcurrentHashMap<String, PublicKey> clientPublicKeys = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        try {
            // 生成服务端RSA密钥对
            serverKeyPair = CryptoUtils.generateRSAKeyPair();
            logger.info("服务端RSA密钥对生成成功");
            
            // 打印公钥供客户端使用（实际项目中应该通过安全渠道分发）
            String publicKeyStr = CryptoUtils.publicKeyToString(serverKeyPair.getPublic());
            logger.info("服务端RSA公钥: {}", publicKeyStr);
            
        } catch (Exception e) {
            logger.error("初始化密钥管理服务失败", e);
            throw new RuntimeException("密钥管理服务初始化失败", e);
        }
    }
    
    /**
     * 获取服务端公钥
     */
    public PublicKey getServerPublicKey() {
        return serverKeyPair.getPublic();
    }
    
    /**
     * 获取服务端私钥
     */
    public PrivateKey getServerPrivateKey() {
        return serverKeyPair.getPrivate();
    }
    
    /**
     * 获取服务端公钥字符串
     */
    public String getServerPublicKeyString() {
        return CryptoUtils.publicKeyToString(serverKeyPair.getPublic());
    }
    
    /**
     * 注册客户端公钥
     */
    public void registerClientPublicKey(String clientId, String publicKeyStr) {
        try {
            PublicKey publicKey = CryptoUtils.stringToPublicKey(publicKeyStr);
            clientPublicKeys.put(clientId, publicKey);
            logger.info("客户端公钥注册成功: {}", clientId);
        } catch (Exception e) {
            logger.error("注册客户端公钥失败: {}", clientId, e);
            throw new RuntimeException("注册客户端公钥失败", e);
        }
    }
    
    /**
     * 获取客户端公钥
     */
    public PublicKey getClientPublicKey(String clientId) {
        return clientPublicKeys.get(clientId);
    }
    
    /**
     * 移除客户端公钥
     */
    public void removeClientPublicKey(String clientId) {
        clientPublicKeys.remove(clientId);
        logger.info("客户端公钥已移除: {}", clientId);
    }
} 