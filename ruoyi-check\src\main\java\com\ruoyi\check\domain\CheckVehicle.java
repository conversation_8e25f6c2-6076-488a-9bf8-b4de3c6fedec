package com.ruoyi.check.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 车辆信息对象 check_vehicle
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class CheckVehicle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 车辆主键 */
    private Long vehicleId;

    /** md_vehicle 表主键 */
    @Excel(name = "md_vehicle 表主键")
    private Long mdVehicleId;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNo;

    /** 车辆识别代码 */
    @Excel(name = "车辆识别代码")
    private String CODE;

    /** 默认司机 */
    @Excel(name = "默认司机")
    private Long driverId;

    /** 车型ID */
    @Excel(name = "车型ID")
    private Long vehicleTypeId;

    /** 车型 */
    @Excel(name = "车型")
    private Long vehicleType;

    /** 行驶证编码 */
    @Excel(name = "行驶证编码")
    private String licenseNo;

    /** 行驶证注册日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "行驶证注册日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date registerDate;

    /** 行驶证发证时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "行驶证发证时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date issueDate;

    /** 行驶证到期日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "行驶证到期日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date issueExpiryDate;

    /** 车主姓名 */
    @Excel(name = "车主姓名")
    private String OWNER;

    /** 道路运输许可证号 */
    @Excel(name = "道路运输许可证号")
    private String roadCard;

    /** 运输证到期日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "运输证到期日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date roadCardExpiryDate;

    /** 承运商ID */
    @Excel(name = "承运商ID")
    private Long carrierId;

    /** 车主身份证 */
    @Excel(name = "车主身份证")
    private String ownerIdcard;

    /** 车主手机 */
    @Excel(name = "车主手机")
    private String ownerPhone;

    /** 创建人id */
    @Excel(name = "创建人id")
    private Long createId;

    /** 修改人id */
    @Excel(name = "修改人id")
    private Long updateId;

    /** 行驶证验证状态(0-待验证、1-验证失败、2-验证不通过、3-验证通过) */
    @Excel(name = "行驶证验证状态(0-待验证、1-验证失败、2-验证不通过、3-验证通过)")
    private Integer issueVerifyStatus;

    /** 行驶证验证信息 */
    @Excel(name = "行驶证验证信息")
    private String issueVerifyMsg;

    /**  道路运输证验证状态(0-待验证、1-验证失败、2-验证不通过、3-验证通过) */
    @Excel(name = " 道路运输证验证状态(0-待验证、1-验证失败、2-验证不通过、3-验证通过)")
    private Integer roadCardVerifyStatus;

    /** 道路运输证验证信息 */
    @Excel(name = "道路运输证验证信息")
    private String roadCardVerifyMsg;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String vclTpNm;

    /** 验证创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "验证创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkCreateTime;

    /** 执行标识。0：未执行，1：已执行 */
    @Excel(name = "执行标识。0：未执行，1：已执行")
    private Long checkStatus;

    public void setVehicleId(Long vehicleId) 
    {
        this.vehicleId = vehicleId;
    }

    public Long getVehicleId() 
    {
        return vehicleId;
    }

    public void setMdVehicleId(Long mdVehicleId) 
    {
        this.mdVehicleId = mdVehicleId;
    }

    public Long getMdVehicleId() 
    {
        return mdVehicleId;
    }

    public void setPlateNo(String plateNo) 
    {
        this.plateNo = plateNo;
    }

    public String getPlateNo() 
    {
        return plateNo;
    }

    public void setCODE(String CODE) 
    {
        this.CODE = CODE;
    }

    public String getCODE() 
    {
        return CODE;
    }

    public void setDriverId(Long driverId) 
    {
        this.driverId = driverId;
    }

    public Long getDriverId() 
    {
        return driverId;
    }

    public void setVehicleTypeId(Long vehicleTypeId) 
    {
        this.vehicleTypeId = vehicleTypeId;
    }

    public Long getVehicleTypeId() 
    {
        return vehicleTypeId;
    }

    public void setVehicleType(Long vehicleType) 
    {
        this.vehicleType = vehicleType;
    }

    public Long getVehicleType() 
    {
        return vehicleType;
    }

    public void setLicenseNo(String licenseNo) 
    {
        this.licenseNo = licenseNo;
    }

    public String getLicenseNo() 
    {
        return licenseNo;
    }

    public void setRegisterDate(Date registerDate) 
    {
        this.registerDate = registerDate;
    }

    public Date getRegisterDate() 
    {
        return registerDate;
    }

    public void setIssueDate(Date issueDate) 
    {
        this.issueDate = issueDate;
    }

    public Date getIssueDate() 
    {
        return issueDate;
    }

    public void setIssueExpiryDate(Date issueExpiryDate) 
    {
        this.issueExpiryDate = issueExpiryDate;
    }

    public Date getIssueExpiryDate() 
    {
        return issueExpiryDate;
    }

    public void setOWNER(String OWNER) 
    {
        this.OWNER = OWNER;
    }

    public String getOWNER() 
    {
        return OWNER;
    }

    public void setRoadCard(String roadCard) 
    {
        this.roadCard = roadCard;
    }

    public String getRoadCard() 
    {
        return roadCard;
    }

    public void setRoadCardExpiryDate(Date roadCardExpiryDate) 
    {
        this.roadCardExpiryDate = roadCardExpiryDate;
    }

    public Date getRoadCardExpiryDate() 
    {
        return roadCardExpiryDate;
    }

    public void setCarrierId(Long carrierId) 
    {
        this.carrierId = carrierId;
    }

    public Long getCarrierId() 
    {
        return carrierId;
    }

    public void setOwnerIdcard(String ownerIdcard) 
    {
        this.ownerIdcard = ownerIdcard;
    }

    public String getOwnerIdcard() 
    {
        return ownerIdcard;
    }

    public void setOwnerPhone(String ownerPhone) 
    {
        this.ownerPhone = ownerPhone;
    }

    public String getOwnerPhone() 
    {
        return ownerPhone;
    }

    public void setCreateId(Long createId) 
    {
        this.createId = createId;
    }

    public Long getCreateId() 
    {
        return createId;
    }

    public void setUpdateId(Long updateId) 
    {
        this.updateId = updateId;
    }

    public Long getUpdateId() 
    {
        return updateId;
    }

    public void setIssueVerifyStatus(Integer issueVerifyStatus) 
    {
        this.issueVerifyStatus = issueVerifyStatus;
    }

    public Integer getIssueVerifyStatus() 
    {
        return issueVerifyStatus;
    }

    public void setIssueVerifyMsg(String issueVerifyMsg) 
    {
        this.issueVerifyMsg = issueVerifyMsg;
    }

    public String getIssueVerifyMsg() 
    {
        return issueVerifyMsg;
    }

    public void setRoadCardVerifyStatus(Integer roadCardVerifyStatus) 
    {
        this.roadCardVerifyStatus = roadCardVerifyStatus;
    }

    public Integer getRoadCardVerifyStatus() 
    {
        return roadCardVerifyStatus;
    }

    public void setRoadCardVerifyMsg(String roadCardVerifyMsg) 
    {
        this.roadCardVerifyMsg = roadCardVerifyMsg;
    }

    public String getRoadCardVerifyMsg() 
    {
        return roadCardVerifyMsg;
    }

    public void setVclTpNm(String vclTpNm) 
    {
        this.vclTpNm = vclTpNm;
    }

    public String getVclTpNm() 
    {
        return vclTpNm;
    }

    public void setCheckCreateTime(Date checkCreateTime) 
    {
        this.checkCreateTime = checkCreateTime;
    }

    public Date getCheckCreateTime() 
    {
        return checkCreateTime;
    }

    public void setCheckStatus(Long checkStatus) 
    {
        this.checkStatus = checkStatus;
    }

    public Long getCheckStatus() 
    {
        return checkStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("vehicleId", getVehicleId())
            .append("mdVehicleId", getMdVehicleId())
            .append("plateNo", getPlateNo())
            .append("CODE", getCODE())
            .append("driverId", getDriverId())
            .append("vehicleTypeId", getVehicleTypeId())
            .append("vehicleType", getVehicleType())
            .append("licenseNo", getLicenseNo())
            .append("registerDate", getRegisterDate())
            .append("issueDate", getIssueDate())
            .append("issueExpiryDate", getIssueExpiryDate())
            .append("OWNER", getOWNER())
            .append("roadCard", getRoadCard())
            .append("roadCardExpiryDate", getRoadCardExpiryDate())
            .append("carrierId", getCarrierId())
            .append("ownerIdcard", getOwnerIdcard())
            .append("ownerPhone", getOwnerPhone())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("issueVerifyStatus", getIssueVerifyStatus())
            .append("issueVerifyMsg", getIssueVerifyMsg())
            .append("roadCardVerifyStatus", getRoadCardVerifyStatus())
            .append("roadCardVerifyMsg", getRoadCardVerifyMsg())
            .append("vclTpNm", getVclTpNm())
            .append("checkCreateTime", getCheckCreateTime())
            .append("checkStatus", getCheckStatus())
            .toString();
    }
}
