# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **********************************************************************************************************************************************************************
        username: qlsz_smolp
        password: vL4QY2lvMn$
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# AES加密配置
aes:
  encryption:
    # AES密钥（32字节，实际项目中应从安全配置中心获取）
    key: MySecretKey12345MySecretKey12345
    # IV向量（16字节）
    iv: 1234567890123456

# 验证接口加密传输配置
driver:
  verify:
    # 是否使用加密传输（默认启用）
    use:
      encryption: true

    # 明文接口URL
    phone:
      url: http://localhost:8973/api/v1/huoda/driver/verify/phone
    idcard:
      url: http://localhost:8973/api/v1/huoda/driver/verify/idcard
    license:
      url: http://localhost:8973/api/v1/huoda/driver/verify/drivinglicense
    qualification:
      url: http://localhost:8973/api/v1/huoda/driver/verify/qualification

#     AES加密接口URL
#    encrypted:
#      phone:
#        url: http://localhost:8973/api/v1/huoda/encrypted/driver/verify/phone
#      idcard:
#        url: http://localhost:8973/api/v1/huoda/encrypted/driver/verify/idcard
#      license:
#        url: http://localhost:8973/api/v1/huoda/encrypted/driver/verify/drivinglicense
#      qualification:
#        url: http://localhost:8973/api/v1/huoda/encrypted/driver/verify/qualification

    encrypted:
      phone:
        url: https://test-gateway.yunxiaobao.com/hdd-open-api/api/v1/huoda/encrypted/driver/verify/phone
      idcard:
        url: https://test-gateway.yunxiaobao.com/hdd-open-api/api/v1/huoda/encrypted/driver/verify/idcard
      license:
        url: https://test-gateway.yunxiaobao.com/hdd-open-api/api/v1/huoda/encrypted/driver/verify/drivinglicense
      qualification:
        url: https://test-gateway.yunxiaobao.com/hdd-open-api/api/v1/huoda/encrypted/driver/verify/qualification


vehicle:
  verify:
    # 是否使用加密传输（默认启用）
    use:
      encryption: true

    # 明文接口URL
    drivinglicense:
      url: http://localhost:8973/api/v1/huoda/vehicle/verify/drivinglicense
    roadtransport:
      url: http://localhost:8973/api/v1/huoda/vehicle/verify/roadtransport

    # AES加密接口URL
#    encrypted:
#      drivinglicense:
#        url: http://localhost:8973/api/v1/huoda/encrypted/vehicle/verify/drivinglicense
#      roadtransport:
#        url: http://localhost:8973/api/v1/huoda/encrypted/vehicle/verify/roadtransport
    encrypted:
      drivinglicense:
        url: https://test-gateway.yunxiaobao.com/hdd-open-api/api/v1/huoda/encrypted/vehicle/verify/drivinglicense
      roadtransport:
        url: https://test-gateway.yunxiaobao.com/hdd-open-api/api/v1/huoda/encrypted/vehicle/verify/roadtransport