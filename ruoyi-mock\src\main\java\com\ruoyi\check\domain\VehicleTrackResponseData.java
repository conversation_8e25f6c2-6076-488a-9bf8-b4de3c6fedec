package com.ruoyi.check.domain;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车辆轨迹响应数据对象
 */
@ApiModel(value = "VehicleTrackResponseData", description = "车辆轨迹响应数据")
public class VehicleTrackResponseData {
    
    @ApiModelProperty("总数")
    private Integer total;  // 总数
    
    @ApiModelProperty("轨迹查询结果列表")
    private List<TrackResult> results;  // 轨迹查询结果列表
    
    public Integer getTotal() {
        return total;
    }
    
    public void setTotal(Integer total) {
        this.total = total;
    }
    
    public List<TrackResult> getResults() {
        return results;
    }
    
    public void setResults(List<TrackResult> results) {
        this.results = results;
    }
    
    /**
     * 轨迹查询结果
     */
    public static class TrackResult {
        private String id;              // 轨迹ID
        private String plateNumber;     // 车牌号
        private String queryStatus;     // 查询状态：success|failed
        private String message;         // 查询结果描述
        private TrackData trackData;    // 轨迹数据
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getQueryStatus() {
            return queryStatus;
        }
        
        public void setQueryStatus(String queryStatus) {
            this.queryStatus = queryStatus;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public TrackData getTrackData() {
            return trackData;
        }
        
        public void setTrackData(TrackData trackData) {
            this.trackData = trackData;
        }
    }
    
    /**
     * 轨迹数据
     */
    public static class TrackData {
        private List<TrackPoint> trackPoints;  // 轨迹点列表
        
        public List<TrackPoint> getTrackPoints() {
            return trackPoints;
        }
        
        public void setTrackPoints(List<TrackPoint> trackPoints) {
            this.trackPoints = trackPoints;
        }
    }
    
    /**
     * 轨迹点
     */
    public static class TrackPoint {
        private String reportTime;  // 上报时间
        private Double longitude;   // 经度
        private Double latitude;    // 纬度
        
        public String getReportTime() {
            return reportTime;
        }
        
        public void setReportTime(String reportTime) {
            this.reportTime = reportTime;
        }
        
        public Double getLongitude() {
            return longitude;
        }
        
        public void setLongitude(Double longitude) {
            this.longitude = longitude;
        }
        
        public Double getLatitude() {
            return latitude;
        }
        
        public void setLatitude(Double latitude) {
            this.latitude = latitude;
        }
    }
} 