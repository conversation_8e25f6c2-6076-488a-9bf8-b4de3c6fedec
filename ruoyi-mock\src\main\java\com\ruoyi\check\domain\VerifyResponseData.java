package com.ruoyi.check.domain;

import java.util.List;
import java.util.Map;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 验证响应数据对象
 */
@ApiModel(value = "VerifyResponseData", description = "验证响应数据")
public class VerifyResponseData {
    
    @ApiModelProperty("验证类型（仅身份证、驾驶证、从业资格证、行驶证、道路运输证验证接口返回）")
    private String verifyType;  // 验证类型（仅身份证、驾驶证、从业资格证、行驶证、道路运输证验证接口返回）
    
    @ApiModelProperty("总数")
    private Integer total;      // 总数
    
    @ApiModelProperty("验证结果列表")
    private List<VerifyResult> results;  // 验证结果列表
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public Integer getTotal() {
        return total;
    }
    
    public void setTotal(Integer total) {
        this.total = total;
    }
    
    public List<VerifyResult> getResults() {
        return results;
    }
    
    public void setResults(List<VerifyResult> results) {
        this.results = results;
    }
    
    /**
     * 验证结果
     */
    @ApiModel(value = "VerifyResult", description = "验证结果")
    public static class VerifyResult {
        @ApiModelProperty("手机号")
        private String phone;                     // 手机号
        
        @ApiModelProperty("身份证号")
        private String idCard;                    // 身份证号
        
        @ApiModelProperty("姓名")
        private String name;                      // 姓名
        
        @ApiModelProperty("驾驶证号码")
        private String licenseNumber;             // 驾驶证号码/从业资格证编号
        
        @ApiModelProperty("从业资格证编号")
        private String qualificationNumber;       // 从业资格证编号
        
        @ApiModelProperty("车牌号")
        private String plateNumber;               // 车牌号
        
        @ApiModelProperty("车辆道路运输证编号")
        private String transportLicenseNumber;    // 车辆道路运输证编号
        
        @ApiModelProperty("车辆所有人")
        private String owner;                     // 车辆所有人
        
        @ApiModelProperty("验证结果：一致|不一致|失效|不存在")
        private String verifyResult;              // 验证结果：一致|不一致|失效|不存在
        
        @ApiModelProperty("验证结果描述")
        private String message;                   // 验证结果描述
        
        @ApiModelProperty("验证结果代码：0-一致，1-不一致，2-失效，3-不存在")
        private Integer resultCode;               // 验证结果代码：0-一致，1-不一致，2-失效，3-不存在
        
        @ApiModelProperty("正确信息（验证不一致时返回）")
        private Map<String, Object> correctInfo;  // 正确信息（验证不一致时返回）
        
        public String getPhone() {
            return phone;
        }
        
        public void setPhone(String phone) {
            this.phone = phone;
        }
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getLicenseNumber() {
            return licenseNumber;
        }
        
        public void setLicenseNumber(String licenseNumber) {
            this.licenseNumber = licenseNumber;
        }
        
        public String getQualificationNumber() {
            return qualificationNumber;
        }
        
        public void setQualificationNumber(String qualificationNumber) {
            this.qualificationNumber = qualificationNumber;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getTransportLicenseNumber() {
            return transportLicenseNumber;
        }
        
        public void setTransportLicenseNumber(String transportLicenseNumber) {
            this.transportLicenseNumber = transportLicenseNumber;
        }
        
        public String getOwner() {
            return owner;
        }
        
        public void setOwner(String owner) {
            this.owner = owner;
        }
        
        public String getVerifyResult() {
            return verifyResult;
        }
        
        public void setVerifyResult(String verifyResult) {
            this.verifyResult = verifyResult;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public Integer getResultCode() {
            return resultCode;
        }
        
        public void setResultCode(Integer resultCode) {
            this.resultCode = resultCode;
        }
        
        public Map<String, Object> getCorrectInfo() {
            return correctInfo;
        }
        
        public void setCorrectInfo(Map<String, Object> correctInfo) {
            this.correctInfo = correctInfo;
        }
    }
} 