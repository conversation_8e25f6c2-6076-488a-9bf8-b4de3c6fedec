<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.check.mapper.CheckVehicleMapper">
    
    <resultMap type="CheckVehicle" id="CheckVehicleResult">
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="mdVehicleId"    column="md_vehicle_id"    />
        <result property="plateNo"    column="PLATE_NO"    />
        <result property="CODE"    column="CODE"    />
        <result property="driverId"    column="driver_id"    />
        <result property="vehicleTypeId"    column="VEHICLE_TYPE_ID"    />
        <result property="vehicleType"    column="VEHICLE_TYPE"    />
        <result property="licenseNo"    column="LICENSE_NO"    />
        <result property="registerDate"    column="REGISTER_DATE"    />
        <result property="issueDate"    column="ISSUE_DATE"    />
        <result property="issueExpiryDate"    column="ISSUE_EXPIRY_DATE"    />
        <result property="OWNER"    column="OWNER"    />
        <result property="roadCard"    column="ROAD_CARD"    />
        <result property="roadCardExpiryDate"    column="ROAD_CARD_EXPIRY_DATE"    />
        <result property="carrierId"    column="CARRIER_ID"    />
        <result property="ownerIdcard"    column="OWNER_IDCARD"    />
        <result property="ownerPhone"    column="OWNER_PHONE"    />
        <result property="createId"    column="CREATE_ID"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateId"    column="UPDATE_ID"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="issueVerifyStatus"    column="ISSUE_VERIFY_STATUS"    />
        <result property="issueVerifyMsg"    column="ISSUE_VERIFY_MSG"    />
        <result property="roadCardVerifyStatus"    column="ROAD_CARD_VERIFY_STATUS"    />
        <result property="roadCardVerifyMsg"    column="ROAD_CARD_VERIFY_MSG"    />
        <result property="vclTpNm"    column="VCL_TP_NM"    />
        <result property="checkCreateTime"    column="CHECK_CREATE_TIME"    />
        <result property="checkStatus"    column="CHECK_STATUS"    />
    </resultMap>

    <sql id="selectCheckVehicleVo">
        select vehicle_id, md_vehicle_id, PLATE_NO, CODE, driver_id, VEHICLE_TYPE_ID, VEHICLE_TYPE, LICENSE_NO, REGISTER_DATE, ISSUE_DATE, ISSUE_EXPIRY_DATE, OWNER, ROAD_CARD, ROAD_CARD_EXPIRY_DATE, CARRIER_ID, OWNER_IDCARD, OWNER_PHONE, CREATE_ID, CREATE_BY, CREATE_TIME, UPDATE_ID, UPDATE_BY, UPDATE_TIME, ISSUE_VERIFY_STATUS, ISSUE_VERIFY_MSG, ROAD_CARD_VERIFY_STATUS, ROAD_CARD_VERIFY_MSG, VCL_TP_NM, CHECK_CREATE_TIME, CHECK_STATUS from check_vehicle
    </sql>

    <select id="selectCheckVehicleList" parameterType="CheckVehicle" resultMap="CheckVehicleResult">
        <include refid="selectCheckVehicleVo"/>
        <where>  
            <if test="mdVehicleId != null "> and md_vehicle_id = #{mdVehicleId}</if>
            <if test="plateNo != null  and plateNo != ''"> and PLATE_NO = #{plateNo}</if>
            <if test="CODE != null  and CODE != ''"> and CODE = #{CODE}</if>
            <if test="driverId != null "> and driver_id = #{driverId}</if>
            <if test="vehicleTypeId != null "> and VEHICLE_TYPE_ID = #{vehicleTypeId}</if>
            <if test="vehicleType != null "> and VEHICLE_TYPE = #{vehicleType}</if>
            <if test="licenseNo != null  and licenseNo != ''"> and LICENSE_NO = #{licenseNo}</if>
            <if test="registerDate != null "> and REGISTER_DATE = #{registerDate}</if>
            <if test="issueDate != null "> and ISSUE_DATE = #{issueDate}</if>
            <if test="issueExpiryDate != null "> and ISSUE_EXPIRY_DATE = #{issueExpiryDate}</if>
            <if test="OWNER != null  and OWNER != ''"> and OWNER = #{OWNER}</if>
            <if test="roadCard != null  and roadCard != ''"> and ROAD_CARD = #{roadCard}</if>
            <if test="roadCardExpiryDate != null "> and ROAD_CARD_EXPIRY_DATE = #{roadCardExpiryDate}</if>
            <if test="carrierId != null "> and CARRIER_ID = #{carrierId}</if>
            <if test="ownerIdcard != null  and ownerIdcard != ''"> and OWNER_IDCARD = #{ownerIdcard}</if>
            <if test="ownerPhone != null  and ownerPhone != ''"> and OWNER_PHONE = #{ownerPhone}</if>
            <if test="createId != null "> and CREATE_ID = #{createId}</if>
            <if test="createBy != null  and createBy != ''"> and CREATE_BY = #{createBy}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updateId != null "> and UPDATE_ID = #{updateId}</if>
            <if test="updateBy != null  and updateBy != ''"> and UPDATE_BY = #{updateBy}</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="issueVerifyStatus != null "> and ISSUE_VERIFY_STATUS = #{issueVerifyStatus}</if>
            <if test="issueVerifyMsg != null  and issueVerifyMsg != ''"> and ISSUE_VERIFY_MSG = #{issueVerifyMsg}</if>
            <if test="roadCardVerifyStatus != null "> and ROAD_CARD_VERIFY_STATUS = #{roadCardVerifyStatus}</if>
            <if test="roadCardVerifyMsg != null  and roadCardVerifyMsg != ''"> and ROAD_CARD_VERIFY_MSG = #{roadCardVerifyMsg}</if>
            <if test="vclTpNm != null  and vclTpNm != ''"> and VCL_TP_NM = #{vclTpNm}</if>
            <if test="checkCreateTime != null "> and CHECK_CREATE_TIME = #{checkCreateTime}</if>
            <if test="checkStatus != null "> and CHECK_STATUS = #{checkStatus}</if>
        </where>
    </select>
    
    <select id="selectCheckVehicleByVehicleId" parameterType="Long" resultMap="CheckVehicleResult">
        <include refid="selectCheckVehicleVo"/>
        where vehicle_id = #{vehicleId}
    </select>

    <insert id="insertCheckVehicle" parameterType="CheckVehicle" useGeneratedKeys="true" keyProperty="vehicleId">
        insert into check_vehicle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mdVehicleId != null">md_vehicle_id,</if>
            <if test="plateNo != null">PLATE_NO,</if>
            <if test="CODE != null">CODE,</if>
            <if test="driverId != null">driver_id,</if>
            <if test="vehicleTypeId != null">VEHICLE_TYPE_ID,</if>
            <if test="vehicleType != null">VEHICLE_TYPE,</if>
            <if test="licenseNo != null">LICENSE_NO,</if>
            <if test="registerDate != null">REGISTER_DATE,</if>
            <if test="issueDate != null">ISSUE_DATE,</if>
            <if test="issueExpiryDate != null">ISSUE_EXPIRY_DATE,</if>
            <if test="OWNER != null">OWNER,</if>
            <if test="roadCard != null">ROAD_CARD,</if>
            <if test="roadCardExpiryDate != null">ROAD_CARD_EXPIRY_DATE,</if>
            <if test="carrierId != null">CARRIER_ID,</if>
            <if test="ownerIdcard != null">OWNER_IDCARD,</if>
            <if test="ownerPhone != null">OWNER_PHONE,</if>
            <if test="createId != null">CREATE_ID,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateId != null">UPDATE_ID,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="issueVerifyStatus != null">ISSUE_VERIFY_STATUS,</if>
            <if test="issueVerifyMsg != null">ISSUE_VERIFY_MSG,</if>
            <if test="roadCardVerifyStatus != null">ROAD_CARD_VERIFY_STATUS,</if>
            <if test="roadCardVerifyMsg != null">ROAD_CARD_VERIFY_MSG,</if>
            <if test="vclTpNm != null">VCL_TP_NM,</if>
            <if test="checkCreateTime != null">CHECK_CREATE_TIME,</if>
            <if test="checkStatus != null">CHECK_STATUS,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mdVehicleId != null">#{mdVehicleId},</if>
            <if test="plateNo != null">#{plateNo},</if>
            <if test="CODE != null">#{CODE},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="vehicleTypeId != null">#{vehicleTypeId},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="licenseNo != null">#{licenseNo},</if>
            <if test="registerDate != null">#{registerDate},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="issueExpiryDate != null">#{issueExpiryDate},</if>
            <if test="OWNER != null">#{OWNER},</if>
            <if test="roadCard != null">#{roadCard},</if>
            <if test="roadCardExpiryDate != null">#{roadCardExpiryDate},</if>
            <if test="carrierId != null">#{carrierId},</if>
            <if test="ownerIdcard != null">#{ownerIdcard},</if>
            <if test="ownerPhone != null">#{ownerPhone},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="issueVerifyStatus != null">#{issueVerifyStatus},</if>
            <if test="issueVerifyMsg != null">#{issueVerifyMsg},</if>
            <if test="roadCardVerifyStatus != null">#{roadCardVerifyStatus},</if>
            <if test="roadCardVerifyMsg != null">#{roadCardVerifyMsg},</if>
            <if test="vclTpNm != null">#{vclTpNm},</if>
            <if test="checkCreateTime != null">#{checkCreateTime},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
         </trim>
    </insert>

    <update id="updateCheckVehicle" parameterType="CheckVehicle">
        update check_vehicle
        <trim prefix="SET" suffixOverrides=",">
            <if test="mdVehicleId != null">md_vehicle_id = #{mdVehicleId},</if>
            <if test="plateNo != null">PLATE_NO = #{plateNo},</if>
            <if test="CODE != null">CODE = #{CODE},</if>
            <if test="driverId != null">driver_id = #{driverId},</if>
            <if test="vehicleTypeId != null">VEHICLE_TYPE_ID = #{vehicleTypeId},</if>
            <if test="vehicleType != null">VEHICLE_TYPE = #{vehicleType},</if>
            <if test="licenseNo != null">LICENSE_NO = #{licenseNo},</if>
            <if test="registerDate != null">REGISTER_DATE = #{registerDate},</if>
            <if test="issueDate != null">ISSUE_DATE = #{issueDate},</if>
            <if test="issueExpiryDate != null">ISSUE_EXPIRY_DATE = #{issueExpiryDate},</if>
            <if test="OWNER != null">OWNER = #{OWNER},</if>
            <if test="roadCard != null">ROAD_CARD = #{roadCard},</if>
            <if test="roadCardExpiryDate != null">ROAD_CARD_EXPIRY_DATE = #{roadCardExpiryDate},</if>
            <if test="carrierId != null">CARRIER_ID = #{carrierId},</if>
            <if test="ownerIdcard != null">OWNER_IDCARD = #{ownerIdcard},</if>
            <if test="ownerPhone != null">OWNER_PHONE = #{ownerPhone},</if>
            <if test="createId != null">CREATE_ID = #{createId},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateId != null">UPDATE_ID = #{updateId},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="issueVerifyStatus != null">ISSUE_VERIFY_STATUS = #{issueVerifyStatus},</if>
            <if test="issueVerifyMsg != null">ISSUE_VERIFY_MSG = #{issueVerifyMsg},</if>
            <if test="roadCardVerifyStatus != null">ROAD_CARD_VERIFY_STATUS = #{roadCardVerifyStatus},</if>
            <if test="roadCardVerifyMsg != null">ROAD_CARD_VERIFY_MSG = #{roadCardVerifyMsg},</if>
            <if test="vclTpNm != null">VCL_TP_NM = #{vclTpNm},</if>
            <if test="checkCreateTime != null">CHECK_CREATE_TIME = #{checkCreateTime},</if>
            <if test="checkStatus != null">CHECK_STATUS = #{checkStatus},</if>
        </trim>
        where vehicle_id = #{vehicleId}
    </update>

    <delete id="deleteCheckVehicleByVehicleId" parameterType="Long">
        delete from check_vehicle where vehicle_id = #{vehicleId}
    </delete>

    <delete id="deleteCheckVehicleByVehicleIds" parameterType="String">
        delete from check_vehicle where vehicle_id in 
        <foreach item="vehicleId" collection="array" open="(" separator="," close=")">
            #{vehicleId}
        </foreach>
    </delete>
    
    <!-- 分页查询车辆列表（用于批量处理） -->
    <select id="selectCheckVehicleListByPage" resultMap="CheckVehicleResult">
        <include refid="selectCheckVehicleVo"/>
        order by vehicle_id
        limit #{limit} offset #{offset}
    </select>
    
    <!-- 统计车辆总数 -->
    <select id="countCheckVehicles" resultType="long">
        select count(*) from check_vehicle
    </select>
</mapper>