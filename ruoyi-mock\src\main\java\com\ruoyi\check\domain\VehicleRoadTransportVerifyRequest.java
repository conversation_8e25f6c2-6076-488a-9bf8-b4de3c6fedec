package com.ruoyi.check.domain;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车辆道路运输证验证请求对象
 */
@ApiModel(value = "VehicleRoadTransportVerifyRequest", description = "车辆道路运输证验证请求")
public class VehicleRoadTransportVerifyRequest {
    
    @ApiModelProperty(value = "验证类型：authenticity（真实性）| validity（有效性）", required = true, example = "authenticity")
    private String verifyType;  // 验证类型：authenticity（真实性）| validity（有效性）
    
    @ApiModelProperty(value = "车辆道路运输证验证数据列表", required = true)
    private List<VehicleRoadTransportData> vehicles;
    
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<VehicleRoadTransportData> getVehicles() {
        return vehicles;
    }
    
    public void setVehicles(List<VehicleRoadTransportData> vehicles) {
        this.vehicles = vehicles;
    }
    
    @ApiModel(value = "VehicleRoadTransportData", description = "车辆道路运输证验证数据")
    public static class VehicleRoadTransportData {
        @ApiModelProperty(value = "车辆道路运输证编号", required = true, example = "陕交运管货字610528000001号")
        private String transportLicenseNumber;  // 车辆道路运输证编号
        
        @ApiModelProperty(value = "车牌号", required = true, example = "陕A12345")
        private String plateNumber;             // 车牌号
        
        @ApiModelProperty(value = "业户名称", required = true, example = "西安市货运有限公司")
        private String businessName;            // 业户名称
        
        @ApiModelProperty(value = "道路运输证到期日", required = true, example = "2025-12-31")
        private String validEndDate;            // 道路运输证到期日
        
        public String getTransportLicenseNumber() {
            return transportLicenseNumber;
        }
        
        public void setTransportLicenseNumber(String transportLicenseNumber) {
            this.transportLicenseNumber = transportLicenseNumber;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getBusinessName() {
            return businessName;
        }
        
        public void setBusinessName(String businessName) {
            this.businessName = businessName;
        }
        
        public String getValidEndDate() {
            return validEndDate;
        }
        
        public void setValidEndDate(String validEndDate) {
            this.validEndDate = validEndDate;
        }
    }
} 