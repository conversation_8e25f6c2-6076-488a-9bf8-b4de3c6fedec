package com.ruoyi.check.domain.dto;

import java.util.List;

/**
 * 从业资格证验证请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class QualificationVerifyRequest {
    
    /**
     * 验证类型（authenticity：真实性验证）
     */
    private String verifyType;
    
    /**
     * 从业资格证数据列表
     */
    private List<QualificationData> qualifications;
    
    /**
     * 从业资格证数据
     */
    public static class QualificationData {
        /**
         * 姓名
         */
        private String name;
        
        /**
         * 从业资格证编号
         */
        private String qualificationNumber;
        
        /**
         * 从业资格证有效期起始日期
         */
        private String validStartDate;
        
        /**
         * 从业资格证有效期结束日期
         */
        private String validEndDate;
        
        // 构造函数
        public QualificationData() {}
        
        public QualificationData(String name, String qualificationNumber, String validStartDate, String validEndDate) {
            this.name = name;
            this.qualificationNumber = qualificationNumber;
            this.validStartDate = validStartDate;
            this.validEndDate = validEndDate;
        }
        
        // Getter和Setter方法
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getQualificationNumber() {
            return qualificationNumber;
        }
        
        public void setQualificationNumber(String qualificationNumber) {
            this.qualificationNumber = qualificationNumber;
        }
        
        public String getValidStartDate() {
            return validStartDate;
        }
        
        public void setValidStartDate(String validStartDate) {
            this.validStartDate = validStartDate;
        }
        
        public String getValidEndDate() {
            return validEndDate;
        }
        
        public void setValidEndDate(String validEndDate) {
            this.validEndDate = validEndDate;
        }
    }
    
    // Getter和Setter方法
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public List<QualificationData> getQualifications() {
        return qualifications;
    }
    
    public void setQualifications(List<QualificationData> qualifications) {
        this.qualifications = qualifications;
    }
} 