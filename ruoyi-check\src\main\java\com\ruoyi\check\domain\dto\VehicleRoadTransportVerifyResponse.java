package com.ruoyi.check.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;

/**
 * 车辆道路运输证验证响应DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@JsonIgnoreProperties(ignoreUnknown = true)  // 忽略未知属性
public class VehicleRoadTransportVerifyResponse {
    
    private String msg;
    private Integer code;
    private VehicleData data;
    
    // 直接映射字段（用于加密接口的直接反序列化）
    private String verifyType;
    private Integer total;
    private List<VehicleResult> results;
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public VehicleData getData() {
        return data;
    }
    
    public void setData(VehicleData data) {
        this.data = data;
    }
    
    // 直接映射字段的getter/setter（用于加密接口）
    public String getVerifyType() {
        return verifyType;
    }
    
    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }
    
    public Integer getTotal() {
        return total;
    }
    
    public void setTotal(Integer total) {
        this.total = total;
    }
    
    public List<VehicleResult> getResults() {
        return results;
    }
    
    public void setResults(List<VehicleResult> results) {
        this.results = results;
    }
    
    public static class VehicleData {
        private String verifyType;
        private Integer total;
        private List<VehicleResult> results;
        
        public String getVerifyType() {
            return verifyType;
        }
        
        public void setVerifyType(String verifyType) {
            this.verifyType = verifyType;
        }
        
        public Integer getTotal() {
            return total;
        }
        
        public void setTotal(Integer total) {
            this.total = total;
        }
        
        public List<VehicleResult> getResults() {
            return results;
        }
        
        public void setResults(List<VehicleResult> results) {
            this.results = results;
        }
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)  // 忽略未知属性
    public static class VehicleResult {
        private String phone;
        private String idCard;
        private String name;
        private String licenseNumber;
        private String qualificationNumber;
        private String plateNumber;
        private String transportLicenseNumber;
        private String owner;
        private String businessName;
        private Boolean formatValid;
        private String verifyResult;
        private String message;
        private String correctInfo;
        
        public String getPhone() {
            return phone;
        }
        
        public void setPhone(String phone) {
            this.phone = phone;
        }
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getLicenseNumber() {
            return licenseNumber;
        }
        
        public void setLicenseNumber(String licenseNumber) {
            this.licenseNumber = licenseNumber;
        }
        
        public String getQualificationNumber() {
            return qualificationNumber;
        }
        
        public void setQualificationNumber(String qualificationNumber) {
            this.qualificationNumber = qualificationNumber;
        }
        
        public String getPlateNumber() {
            return plateNumber;
        }
        
        public void setPlateNumber(String plateNumber) {
            this.plateNumber = plateNumber;
        }
        
        public String getTransportLicenseNumber() {
            return transportLicenseNumber;
        }
        
        public void setTransportLicenseNumber(String transportLicenseNumber) {
            this.transportLicenseNumber = transportLicenseNumber;
        }
        
        public String getOwner() {
            return owner;
        }
        
        public void setOwner(String owner) {
            this.owner = owner;
        }
        
        public String getBusinessName() {
            return businessName;
        }
        
        public void setBusinessName(String businessName) {
            this.businessName = businessName;
        }
        
        public Boolean getFormatValid() {
            return formatValid;
        }
        
        public void setFormatValid(Boolean formatValid) {
            this.formatValid = formatValid;
        }
        
        public String getVerifyResult() {
            return verifyResult;
        }
        
        public void setVerifyResult(String verifyResult) {
            this.verifyResult = verifyResult;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public String getCorrectInfo() {
            return correctInfo;
        }
        
        public void setCorrectInfo(String correctInfo) {
            this.correctInfo = correctInfo;
        }
    }
}
