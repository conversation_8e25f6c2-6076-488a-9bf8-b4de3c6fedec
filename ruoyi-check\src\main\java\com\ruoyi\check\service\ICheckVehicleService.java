package com.ruoyi.check.service;

import java.util.List;
import com.ruoyi.check.domain.CheckVehicle;

/**
 * 车辆信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface ICheckVehicleService 
{
    /**
     * 查询车辆信息
     * 
     * @param vehicleId 车辆信息主键
     * @return 车辆信息
     */
    public CheckVehicle selectCheckVehicleByVehicleId(Long vehicleId);

    /**
     * 查询车辆信息列表
     * 
     * @param checkVehicle 车辆信息
     * @return 车辆信息集合
     */
    public List<CheckVehicle> selectCheckVehicleList(CheckVehicle checkVehicle);

    /**
     * 新增车辆信息
     * 
     * @param checkVehicle 车辆信息
     * @return 结果
     */
    public int insertCheckVehicle(CheckVehicle checkVehicle);

    /**
     * 修改车辆信息
     * 
     * @param checkVehicle 车辆信息
     * @return 结果
     */
    public int updateCheckVehicle(CheckVehicle checkVehicle);

    /**
     * 批量删除车辆信息
     * 
     * @param vehicleIds 需要删除的车辆信息主键集合
     * @return 结果
     */
    public int deleteCheckVehicleByVehicleIds(Long[] vehicleIds);

    /**
     * 删除车辆信息信息
     * 
     * @param vehicleId 车辆信息主键
     * @return 结果
     */
    public int deleteCheckVehicleByVehicleId(Long vehicleId);
    
    /**
     * 分页查询车辆列表（用于批量处理）
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 车辆集合
     */
    public List<CheckVehicle> selectCheckVehicleListByPage(Long offset, Integer limit);
    
    /**
     * 统计车辆总数
     * 
     * @return 总数
     */
    public long countCheckVehicles();
}
