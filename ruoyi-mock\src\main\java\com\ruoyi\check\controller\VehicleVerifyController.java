package com.ruoyi.check.controller;

import com.ruoyi.check.domain.*;
import com.ruoyi.check.service.VehicleVerifyService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 车辆验证控制器
 */
@Api(tags = "货达Mock服务-车辆验证接口", description = "提供车辆相关证件的批量验证服务")
@RestController
@RequestMapping("/api/v1/huoda/vehicle/verify")
public class VehicleVerifyController extends BaseController {
    
    @Autowired
    private VehicleVerifyService vehicleVerifyService;
    
    /**
     * 行驶证批量验证接口
     */
    @ApiOperation(value = "车辆行驶证批量验证", 
                  notes = "验证车辆行驶证信息的真实性和有效性。支持验证车牌号、车辆识别代码、车辆类型、所有人、有效期等信息的一致性。\n\n" +
                         "请求示例：\n" +
                         "```json\n" +
                         "{\n" +
                         "  \"verifyType\": \"authenticity\",\n" +
                         "  \"vehicles\": [\n" +
                         "    {\n" +
                         "      \"plateNumber\": \"陕A12345\",\n" +
                         "      \"vehicleIdentificationCode\": \"LSGGG54X8DS123456\",\n" +
                         "      \"vehicleType\": \"货车\",\n" +
                         "      \"owner\": \"西安市货运有限公司\",\n" +
                         "      \"validEndDate\": \"2025-12-31\"\n" +
                         "    }\n" +
                         "  ]\n" +
                         "}\n" +
                         "```")
    @ApiImplicitParam(name = "request", value = "行驶证验证请求参数", required = true, dataTypeClass = VehicleDrivingLicenseVerifyRequest.class)
    @Anonymous
    @PostMapping("/drivinglicense")
    public AjaxResult verifyDrivingLicense(@RequestBody VehicleDrivingLicenseVerifyRequest request) {
        VerifyResponseData responseData = vehicleVerifyService.verifyDrivingLicense(request);
        return AjaxResult.success("请求处理成功", responseData);
    }
    
    /**
     * 车辆道路运输证批量验证接口
     */
    @ApiOperation(value = "车辆道路运输证批量验证", 
                  notes = "验证车辆道路运输证信息的真实性和有效性。支持验证运输证编号、车牌号、业户名称、有效期等信息的一致性。\n\n" +
                         "请求示例：\n" +
                         "```json\n" +
                         "{\n" +
                         "  \"verifyType\": \"authenticity\",\n" +
                         "  \"vehicles\": [\n" +
                         "    {\n" +
                         "      \"transportLicenseNumber\": \"陕交运管货字610528000001号\",\n" +
                         "      \"plateNumber\": \"陕A12345\",\n" +
                         "      \"businessName\": \"西安市货运有限公司\",\n" +
                         "      \"validEndDate\": \"2025-12-31\"\n" +
                         "    }\n" +
                         "  ]\n" +
                         "}\n" +
                         "```")
    @ApiImplicitParam(name = "request", value = "道路运输证验证请求参数", required = true, dataTypeClass = VehicleRoadTransportVerifyRequest.class)
    @Anonymous
    @PostMapping("/roadtransport")
    public AjaxResult verifyRoadTransport(@RequestBody VehicleRoadTransportVerifyRequest request) {
        VerifyResponseData responseData = vehicleVerifyService.verifyRoadTransport(request);
        return AjaxResult.success("请求处理成功", responseData);
    }
} 