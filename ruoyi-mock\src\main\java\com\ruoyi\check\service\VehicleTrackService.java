package com.ruoyi.check.service;

import com.ruoyi.check.domain.*;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 车辆轨迹服务
 */
@Service
public class VehicleTrackService {
    
    private final Random random = new Random();
    
    /**
     * 车辆历史轨迹查询
     */
    public VehicleTrackResponseData queryTrackHistory(VehicleTrackHistoryRequest request) {
        VehicleTrackResponseData responseData = new VehicleTrackResponseData();
        responseData.setTotal(request.getVehicles().size());
        
        List<VehicleTrackResponseData.TrackResult> results = new ArrayList<>();
        for (VehicleTrackHistoryRequest.VehicleTrackData data : request.getVehicles()) {
            VehicleTrackResponseData.TrackResult result = new VehicleTrackResponseData.TrackResult();
            result.setId("T" + String.format("%03d", random.nextInt(1000)));
            result.setPlateNumber(data.getPlateNumber());
            result.setQueryStatus("success");
            result.setMessage("轨迹数据获取成功");
            
            // 生成模拟轨迹数据
            VehicleTrackResponseData.TrackData trackData = new VehicleTrackResponseData.TrackData();
            List<VehicleTrackResponseData.TrackPoint> trackPoints = new ArrayList<>();
            
            // 模拟生成轨迹点（从西安到北京的大致路线）
            double[] baseLongitudes = {108.9423, 109.5, 110.2, 111.8, 113.5, 115.2, 116.4};
            double[] baseLatitudes = {34.2658, 35.1, 36.2, 37.8, 38.5, 39.2, 39.9};
            
            for (int i = 0; i < 7; i++) {
                VehicleTrackResponseData.TrackPoint point = new VehicleTrackResponseData.TrackPoint();
                point.setReportTime("2024-12-0" + (i + 1) + "T" + String.format("%02d", 8 + i) + ":00:00Z");
                point.setLongitude(baseLongitudes[i] + (random.nextDouble() - 0.5) * 0.01);
                point.setLatitude(baseLatitudes[i] + (random.nextDouble() - 0.5) * 0.01);
                trackPoints.add(point);
            }
            
            trackData.setTrackPoints(trackPoints);
            result.setTrackData(trackData);
            
            results.add(result);
        }
        
        responseData.setResults(results);
        return responseData;
    }
} 