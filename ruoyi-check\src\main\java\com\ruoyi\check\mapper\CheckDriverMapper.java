package com.ruoyi.check.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.check.domain.CheckDriver;

/**
 * 司机Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface CheckDriverMapper 
{
    /**
     * 查询司机
     * 
     * @param driverId 司机主键
     * @return 司机
     */
    public CheckDriver selectCheckDriverByDriverId(Long driverId);

    /**
     * 查询司机列表
     * 
     * @param checkDriver 司机
     * @return 司机集合
     */
    public List<CheckDriver> selectCheckDriverList(CheckDriver checkDriver);

    /**
     * 新增司机
     * 
     * @param checkDriver 司机
     * @return 结果
     */
    public int insertCheckDriver(CheckDriver checkDriver);

    /**
     * 修改司机
     * 
     * @param checkDriver 司机
     * @return 结果
     */
    public int updateCheckDriver(CheckDriver checkDriver);

    /**
     * 删除司机
     * 
     * @param driverId 司机主键
     * @return 结果
     */
    public int deleteCheckDriverByDriverId(Long driverId);

    /**
     * 批量删除司机
     * 
     * @param driverIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckDriverByDriverIds(Long[] driverIds);
    
    /**
     * 分页查询司机列表（用于批量处理）
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 司机集合
     */
    public List<CheckDriver> selectCheckDriverListByPage(@Param("offset") Long offset, @Param("limit") Integer limit);
    
    /**
     * 统计司机总数
     * 
     * @return 总数
     */
    public long countCheckDrivers();
}
