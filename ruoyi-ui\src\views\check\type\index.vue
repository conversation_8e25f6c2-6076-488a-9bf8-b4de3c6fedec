<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="验证的数据" prop="checkData">
        <el-input
          v-model="queryParams.checkData"
          placeholder="请输入验证的数据"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="checkName">
        <el-input
          v-model="queryParams.checkName"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="货达验证结果" prop="hdResult">
        <el-input
          v-model="queryParams.hdResult"
          placeholder="请输入货达验证结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="和硕验证结果" prop="hsResult">
        <el-input
          v-model="queryParams.hsResult"
          placeholder="请输入和硕验证结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物泊验证结果" prop="wbResult">
        <el-input
          v-model="queryParams.wbResult"
          placeholder="请输入物泊验证结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="卡一车验证结果" prop="kycResult">
        <el-input
          v-model="queryParams.kycResult"
          placeholder="请输入卡一车验证结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="大道成验证结果" prop="ddcResult">
        <el-input
          v-model="queryParams.ddcResult"
          placeholder="请输入大道成验证结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['check:type:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['check:type:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['check:type:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['check:type:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="类型ID" align="center" prop="typeId" />
      <el-table-column label="验证的数据" align="center" prop="checkData" />
      <el-table-column label="名称" align="center" prop="checkName" />
      <el-table-column label="验证类型" align="center" prop="checkStatus" />
      <el-table-column label="数据类型" align="center" prop="dataStatus" />
      <el-table-column label="货达验证结果" align="center" prop="hdResult" />
      <el-table-column label="和硕验证结果" align="center" prop="hsResult" />
      <el-table-column label="物泊验证结果" align="center" prop="wbResult" />
      <el-table-column label="卡一车验证结果" align="center" prop="kycResult" />
      <el-table-column label="大道成验证结果" align="center" prop="ddcResult" />
      <el-table-column label="详情" align="center" prop="detail" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['check:type:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['check:type:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改验证对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="验证的数据" prop="checkData">
          <el-input v-model="form.checkData" placeholder="请输入验证的数据" />
        </el-form-item>
        <el-form-item label="名称" prop="checkName">
          <el-input v-model="form.checkName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="货达验证结果" prop="hdResult">
          <el-input v-model="form.hdResult" placeholder="请输入货达验证结果" />
        </el-form-item>
        <el-form-item label="和硕验证结果" prop="hsResult">
          <el-input v-model="form.hsResult" placeholder="请输入和硕验证结果" />
        </el-form-item>
        <el-form-item label="物泊验证结果" prop="wbResult">
          <el-input v-model="form.wbResult" placeholder="请输入物泊验证结果" />
        </el-form-item>
        <el-form-item label="卡一车验证结果" prop="kycResult">
          <el-input v-model="form.kycResult" placeholder="请输入卡一车验证结果" />
        </el-form-item>
        <el-form-item label="大道成验证结果" prop="ddcResult">
          <el-input v-model="form.ddcResult" placeholder="请输入大道成验证结果" />
        </el-form-item>
        <el-form-item label="详情" prop="detail">
          <el-input v-model="form.detail" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listType, getType, delType, addType, updateType } from "@/api/check/type"

export default {
  name: "Type",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 验证表格数据
      typeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkData: null,
        checkName: null,
        checkStatus: null,
        dataStatus: null,
        hdResult: null,
        hsResult: null,
        wbResult: null,
        kycResult: null,
        ddcResult: null,
        detail: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        hdResult: [
          { required: true, message: "货达验证结果不能为空", trigger: "blur" }
        ],
        hsResult: [
          { required: true, message: "和硕验证结果不能为空", trigger: "blur" }
        ],
        wbResult: [
          { required: true, message: "物泊验证结果不能为空", trigger: "blur" }
        ],
        kycResult: [
          { required: true, message: "卡一车验证结果不能为空", trigger: "blur" }
        ],
        ddcResult: [
          { required: true, message: "大道成验证结果不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询验证列表 */
    getList() {
      this.loading = true
      listType(this.queryParams).then(response => {
        this.typeList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        typeId: null,
        checkData: null,
        checkName: null,
        checkStatus: null,
        dataStatus: null,
        hdResult: null,
        hsResult: null,
        wbResult: null,
        kycResult: null,
        ddcResult: null,
        detail: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.typeId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加验证"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const typeId = row.typeId || this.ids
      getType(typeId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改验证"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.typeId != null) {
            updateType(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addType(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const typeIds = row.typeId || this.ids
      this.$modal.confirm('是否确认删除验证编号为"' + typeIds + '"的数据项？').then(function() {
        return delType(typeIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('check/type/export', {
        ...this.queryParams
      }, `type_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
