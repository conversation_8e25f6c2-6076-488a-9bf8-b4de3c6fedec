# 多阶段构建：第一阶段用于编译项目
FROM maven:3.8.6-openjdk-8-slim AS builder

# 设置工作目录
WORKDIR /build

# 复制pom文件（利用Docker缓存层）
COPY pom.xml .
COPY ruoyi-admin/pom.xml ruoyi-admin/
COPY ruoyi-framework/pom.xml ruoyi-framework/
COPY ruoyi-system/pom.xml ruoyi-system/
COPY ruoyi-quartz/pom.xml ruoyi-quartz/
COPY ruoyi-generator/pom.xml ruoyi-generator/
COPY ruoyi-common/pom.xml ruoyi-common/
COPY ruoyi-check/pom.xml ruoyi-check/
COPY ruoyi-mock/pom.xml ruoyi-mock/

# 复制源代码
COPY . .

# 编译项目
RUN mvn clean package --D maven.test.skip=true -B

# 第二阶段：运行时镜像
FROM openjdk:8-jre-alpine

# 设置维护者信息
LABEL maintainer="ruoyi-admin"

# 设置工作目录
WORKDIR /app

# 设置时区为上海
RUN apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 创建应用目录和日志目录
RUN mkdir -p /app/logs && \
    mkdir -p /app/uploadPath && \
    mkdir -p /home/<USER>/logs

# 从构建阶段复制编译好的jar包
COPY --from=builder /build/ruoyi-admin/target/ruoyi-admin.jar /app/app.jar

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom"

# 暴露端口
EXPOSE 8973

# 设置启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar"]